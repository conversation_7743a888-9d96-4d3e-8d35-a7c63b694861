<template>
  <div :class="[$style.fieldNode, { [$style.expanded]: expanded }]" :style="{ marginLeft: `${level * 20}px` }">
    <!-- 字段头部 -->
    <div :class="$style.fieldHeader" @click="toggleExpanded">
      <div :class="$style.fieldInfo">
        <span :class="$style.expandIcon" v-if="canHaveChildren">
          {{ expanded ? '▼' : '▶' }}
        </span>
        <span :class="$style.fieldName">{{ fieldKey }}</span>
        <a-tag :color="getTypeColor(localField.type)" size="small">{{ localField.type }}</a-tag>
        <span v-if="localField.description" :class="$style.fieldDesc">{{ localField.description }}</span>
      </div>
      <div :class="$style.fieldActions">
        <a-button v-if="canHaveChildren" size="small" type="text" @click.stop="addChild">
          <PlusOutlined />
        </a-button>
        <a-button size="small" type="text" @click.stop="deleteField">
          <DeleteOutlined />
        </a-button>
      </div>
    </div>

    <!-- 字段配置 -->
    <div v-if="expanded" :class="$style.fieldConfig">
      <a-form layout="vertical" size="small">
        <a-row :gutter="12">
          <!-- 字段名 -->
          <a-col :span="8">
            <a-form-item label="字段名">
              <a-input
                v-model:value="localFieldKey"
                @blur="handleKeyChange"
                @pressEnter="handleKeyChange"
                placeholder="字段名"
              />
            </a-form-item>
          </a-col>
          
          <!-- 类型 -->
          <a-col :span="8">
            <a-form-item label="类型">
              <a-select
                v-model:value="localField.type"
                @change="handleTypeChange"
              >
                <a-select-option value="string">字符串</a-select-option>
                <a-select-option value="number">数字</a-select-option>
                <a-select-option value="integer">整数</a-select-option>
                <a-select-option value="boolean">布尔值</a-select-option>
                <a-select-option value="array">数组</a-select-option>
                <a-select-option value="object">对象</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <!-- 必需 -->
          <a-col :span="8">
            <a-form-item label="必需">
              <a-switch
                v-model:checked="isRequired"
                @change="handleRequiredChange"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 描述 -->
        <a-form-item label="描述">
          <a-input
            v-model:value="localField.description"
            @change="updateField"
            placeholder="字段描述"
          />
        </a-form-item>

        <!-- 字符串特有配置 -->
        <div v-if="localField.type === 'string'">
          <a-form-item label="枚举值">
            <a-select
              v-model:value="localField.enum"
              @change="updateField"
              mode="tags"
              placeholder="输入可选值（可选）"
              allow-clear
            />
          </a-form-item>
        </div>

        <!-- 数字特有配置 -->
        <div v-if="['number', 'integer'].includes(localField.type)">
          <!-- 移除了最小值最大值限制，保持数字类型的灵活性 -->
        </div>

        <!-- 数组特有配置 -->
        <div v-if="localField.type === 'array'">
          
          <a-form-item label="项目类型">
            <a-select
              v-model:value="itemType"
              @change="handleItemTypeChange"
            >
              <a-select-option value="string">字符串</a-select-option>
              <a-select-option value="number">数字</a-select-option>
              <a-select-option value="integer">整数</a-select-option>
              <a-select-option value="boolean">布尔值</a-select-option>
              <a-select-option value="object">对象</a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </a-form>
    </div>

    <!-- 子字段 -->
    <div v-if="expanded && hasChildren" :class="$style.children">
      <!-- 对象的属性 -->
      <FieldNode
        v-if="localField.type === 'object' && localField.properties"
        v-for="(child, childKey) in localField.properties"
        :key="childKey"
        :field-key="childKey"
        :field-data="child"
        :path="[...path, childKey]"
        :level="level + 1"
        @update="handleChildUpdate"
        @delete="handleChildDelete"
        @add-child="handleChildAddChild"
      />

      <!-- 数组项的属性 -->
      <div v-if="localField.type === 'array' && localField.items && localField.items.type === 'object'">
        <div :class="$style.arrayItemsHeader">
          <span>数组项结构:</span>
        </div>
        <FieldNode
          v-for="(child, childKey) in localField.items.properties"
          :key="childKey"
          :field-key="childKey"
          :field-data="child"
          :path="[...path, 'items', childKey]"
          :level="level + 1"
          @update="handleChildUpdate"
          @delete="handleChildDelete"
          @add-child="handleChildAddChild"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  fieldKey: {
    type: String,
    required: true
  },
  fieldData: {
    type: Object,
    required: true
  },
  path: {
    type: Array,
    required: true
  },
  level: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update', 'delete', 'add-child'])

// 本地状态
const expanded = ref(true)
const localField = ref({ ...props.fieldData })
const localFieldKey = ref(props.fieldKey)

// 监听props变化
watch(() => props.fieldKey, (newKey) => {
  localFieldKey.value = newKey
})

watch(() => props.fieldData, (newData) => {
  localField.value = { ...newData }
}, { deep: true })

// 计算属性
const canHaveChildren = computed(() => {
  return ['object', 'array'].includes(localField.value.type)
})

const hasChildren = computed(() => {
  if (localField.value.type === 'object') {
    return localField.value.properties && Object.keys(localField.value.properties).length > 0
  }
  if (localField.value.type === 'array') {
    return localField.value.items && localField.value.items.type === 'object' && 
           localField.value.items.properties && Object.keys(localField.value.items.properties).length > 0
  }
  return false
})

const isRequired = computed({
  get: () => {
    // 从父级获取required信息
    return props.fieldData.required || false
  },
  set: (value) => {
    // 更新字段的required属性
    emit('update', props.path, { ...localField.value, required: value })
  }
})

const itemType = computed({
  get: () => {
    return localField.value.items?.type || 'string'
  },
  set: (value) => {
    if (!localField.value.items) {
      localField.value.items = {}
    }
    localField.value.items.type = value
    if (value === 'object') {
      localField.value.items.properties = {}
      localField.value.items.required = []
    }
    updateField()
  }
})

// 方法
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

const getTypeColor = (type) => {
  const colors = {
    string: 'blue',
    number: 'green',
    integer: 'green',
    boolean: 'orange',
    array: 'purple',
    object: 'red'
  }
  return colors[type] || 'default'
}

const updateField = () => {
  emit('update', props.path, { ...localField.value })
}

const handleKeyChange = () => {
  // 字段名变更需要特殊处理
  const newKey = localFieldKey.value
  if (newKey && newKey !== props.fieldKey) {
    emit('update', props.path, { ...localField.value }, newKey)
  }
}

const handleTypeChange = (newType) => {
  // 类型变更时清理不相关的属性
  const cleanField = {
    type: newType,
    description: localField.value.description || ''
  }
  
  // 根据新类型初始化特定属性
  if (newType === 'object') {
    cleanField.properties = {}
    cleanField.required = []
  } else if (newType === 'array') {
    cleanField.items = { type: 'string' }
  }
  
  localField.value = cleanField
  updateField()
}

const handleItemTypeChange = (newType) => {
  itemType.value = newType
}

const handleRequiredChange = (required) => {
  // 处理必需字段变更 - 通知父组件更新required数组
  emit('required-change', props.path, props.fieldKey, required)
}

const addChild = () => {
  emit('add-child', props.path)
}

const deleteField = () => {
  emit('delete', props.path)
}

// 处理子组件事件
const handleChildUpdate = (path, updates, newKey) => {
  emit('update', path, updates, newKey)
}

const handleChildDelete = (path) => {
  emit('delete', path)
}

const handleChildAddChild = (path) => {
  emit('add-child', path)
}

// 监听外部数据变化
watch(() => props.fieldData, (newData) => {
  localField.value = { ...newData }
}, { deep: true })
</script>

<style module>
.fieldNode {
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fff;
  transition: all 0.3s ease;
}

.fieldNode:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.fieldNode.expanded {
  border-color: #1890ff;
}

.fieldHeader {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.fieldNode:not(.expanded) .fieldHeader {
  border-radius: 6px;
}

.fieldInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.expandIcon {
  font-size: 12px;
  color: #8c8c8c;
  width: 12px;
  text-align: center;
}

.fieldName {
  font-weight: 500;
  color: #262626;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.fieldDesc {
  color: #8c8c8c;
  font-size: 12px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fieldActions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fieldNode:hover .fieldActions {
  opacity: 1;
}

.fieldConfig {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
}

.children {
  padding: 8px 16px 16px;
  background: #f9f9f9;
  border-top: 1px solid #f0f0f0;
}

.arrayItemsHeader {
  padding: 8px 0;
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 8px;
}

/* 响应式设计 */
/* 超宽屏优化 */
@media (min-width: 2560px) {
  .fieldHeader {
    padding: 10px 16px;
  }

  .fieldConfig {
    padding: 20px;
  }

  .fieldInfo {
    gap: 12px;
  }

  .fieldName {
    font-size: 14px;
  }
}

/* 大屏优化 */
@media (min-width: 1920px) {
  .fieldHeader {
    padding: 9px 14px;
  }

  .fieldConfig {
    padding: 18px;
  }
}

/* 中等屏幕 */
@media (max-width: 1200px) {
  .fieldInfo {
    gap: 6px;
  }
}

@media (max-width: 768px) {
  .fieldHeader {
    padding: 6px 8px;
  }

  .fieldConfig {
    padding: 12px;
  }

  .children {
    padding: 6px 12px 12px;
  }

  .fieldInfo {
    gap: 4px;
  }

  .fieldName {
    font-size: 12px;
  }

  .fieldDesc {
    display: none; /* 移动端隐藏描述以节省空间 */
  }
}
</style>
