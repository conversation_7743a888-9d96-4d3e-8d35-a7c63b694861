#!/usr/bin/env python3
"""
修改智能体提示词脚本
优化智能体的工具调用能力
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例


async def 检查智能体知识库关联():
    """检查智能体是否关联了知识库"""
    try:
        # 检查智能体RAG配置
        检查SQL = """
        SELECT 启用rag
        FROM langchain_智能体配置表
        WHERE id = 5
        """
        结果 = await Postgre_异步连接池实例.执行查询(检查SQL)

        if 结果:
            智能体配置 = 结果[0]
            print(f"📊 智能体RAG状态: {'✅ 已启用' if 智能体配置['启用rag'] else '❌ 未启用'}")
            print("📊 RAG配置现在存储在知识库关联表中")

        # 检查可用的知识库
        知识库SQL = """
        SELECT id, 知识库名称, 文档数量, 知识库状态
        FROM langchain_知识库表
        WHERE 知识库状态 = '正常' AND 文档数量 > 0
        ORDER BY id
        """
        知识库结果 = await Postgre_异步连接池实例.执行查询(知识库SQL)

        if 知识库结果:
            print(f"\n📚 可用知识库 ({len(知识库结果)}个):")
            for 知识库 in 知识库结果:
                print(f"   ID {知识库['id']}: {知识库['知识库名称']} ({知识库['文档数量']}个文档)")

        return len(知识库结果) > 0

    except Exception as e:
        print(f"❌ 检查知识库关联失败: {str(e)}")
        return False


async def 修改智能体提示词():
    """修改智能体5的提示词，增强工具调用能力"""
    try:
        # 确保数据库连接池已初始化
        if not Postgre_异步连接池实例.已初始化:
            await Postgre_异步连接池实例.初始化数据库连接池()
        
        # 新的提示词，通用版本，不硬编码特定业务逻辑
        新提示词 = """你是一个专业的智能客服助手，负责处理客户咨询和提供优质服务。

**核心职责：**
1. 友好回应客户问候和咨询
2. 提供准确的产品信息和服务支持
3. 根据需要使用可用工具完成任务

**工作原则：**
- 语言友好、专业、简洁
- 基于知识库内容回答相关问题
- 根据对话需要合理使用工具
- 确保信息准确性和时效性

**工具使用指导：**
- 根据用户需求和对话上下文决定是否使用工具
- 严格按照ReAct格式进行工具调用
- 完成所有必要的工具调用后，输出"Final Answer:"开头的最终回复
- 工具参数格式要正确，避免传递复杂对象

**回复要求：**
- 回答要准确、有帮助
- 语言表达要自然流畅
- 根据用户问题的复杂程度调整回复详细程度"""

        # 更新SQL
        更新SQL = """
        UPDATE langchain_智能体配置表
        SET 系统提示词 = $1
        WHERE id = 5
        """
        
        print("🔄 开始修改智能体提示词...")
        
        # 执行更新
        await Postgre_异步连接池实例.执行更新(更新SQL, (新提示词,))

        print("✅ 智能体提示词修改成功！")

        # 重新启用RAG并调整温度参数以提高输出格式稳定性
        优化配置SQL = """
        UPDATE langchain_智能体配置表
        SET 启用rag = true, 温度参数 = 0.3
        WHERE id = 5
        """
        await Postgre_异步连接池实例.执行更新(优化配置SQL)
        print("✅ 已重新启用RAG并调整温度参数为0.3（提高输出格式稳定性）")
        
        # 验证修改结果
        验证SQL = "SELECT 系统提示词 FROM langchain_智能体配置表 WHERE id = 5"
        结果 = await Postgre_异步连接池实例.执行查询(验证SQL)
        
        if 结果:
            当前提示词 = 结果[0]["系统提示词"]
            print(f"📋 当前提示词长度: {len(当前提示词)} 字符")
            print(f"📋 提示词预览: {当前提示词[:100]}...")
        
    except Exception as e:
        print(f"❌ 修改失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("=" * 50)
    print("智能体提示词修改工具")
    print("=" * 50)

    # 先检查知识库关联情况
    print("🔍 检查智能体知识库关联情况...")
    有知识库 = await 检查智能体知识库关联()

    if not 有知识库:
        print("⚠️  警告：没有可用的知识库，RAG功能可能无法正常工作")

    print("\n🔄 开始修改智能体提示词...")
    await 修改智能体提示词()

    print("\n" + "=" * 50)
    print("修改完成！现在可以测试智能体对话功能")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
