"""
LangChain复合智能体核心
统一的智能体架构，整合所有功能为单一复合智能体

核心功能：
1. RAG检索功能 - 集成检索增强生成
2. 工具动态加载 - 支持从数据库动态加载和配置工具
3. 工具循环调用 - 支持智能体在对话过程中多次调用不同工具
4. JSON格式化输出 - 确保输出结果为标准JSON格式
5. 自定义变量支持 - 允许在对话过程中使用和管理自定义变量
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass

# 数据层导入
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 数据.LangChain_模型数据层 import LangChain模型数据层实例

# 服务层导入
from 服务.LangChain_模型管理器 import LangChain模型管理器实例
from 服务.LangChain_知识库服务 import LangChain知识库服务实例
from 服务.LangChain_工具管理器 import LangChain工具管理器实例
from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

# 日志导入
from 日志 import 应用日志器 as 复合智能体日志器
from 状态 import 状态

# 复合智能体常量定义
class 复合智能体常量:
    """复合智能体常量定义"""

    # RAG检索配置
    默认最大检索数量: int = 5
    默认相似度阈值: float = 0.7
    默认检索策略: str = "similarity"

    # 模型配置
    默认温度: float = 0.7
    默认最大令牌数: int = 4000

    # 超时配置
    对话超时秒数: int = 30
    检索超时秒数: int = 10
    工具调用超时秒数: int = 15

    # 缓存配置
    实例缓存大小: int = 100

# LangChain组件导入 - 要求必须正确安装
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import BaseTool
from langchain.agents import AgentExecutor, create_react_agent
from langchain_core.prompts import PromptTemplate


@dataclass
class 复合智能体配置:
    """复合智能体配置数据类"""
    用户id: int
    智能体名称: str
    智能体id: Optional[int] = None
    模型名称: str = "qwen-turbo"
    系统提示词: str = ""
    温度参数: float = 0.7
    最大令牌数: int = 4000
    记忆窗口大小: int = 10
    启用rag: bool = False
    工具列表: Optional[List[str]] = None
    输出格式: str = "text"
    自定义回复格式: Optional[Dict[str, Any]] = None
    自定义变量: Optional[List[Dict[str, Any]]] = None

    def __post_init__(self):
        if self.工具列表 is None:
            self.工具列表 = []
        if self.自定义变量 is None:
            self.自定义变量 = []


class 复合智能体实例:
    """复合智能体运行时实例 - 整合所有功能"""

    def __init__(self, 配置: 复合智能体配置, 实例ID: str):
        self.配置: 复合智能体配置 = 配置
        self.实例ID: str = 实例ID
        self.创建时间: datetime = datetime.now()

        # 核心组件
        self.模型: Optional[Any] = None
        self.工具列表: List[Any] = []
        self.智能体执行器: Optional[Any] = None
        self.已初始化: bool = False

        # RAG组件
        self.rag_启用: bool = 配置.启用rag

        # 记忆组件
        self.对话历史: List[Dict[str, Any]] = []

        # 令牌消耗统计
        self.最后对话令牌消耗: int = 0
        self.总令牌消耗: int = 0

        复合智能体日志器.info(f"复合智能体实例创建: {实例ID}")

    async def 初始化(self) -> None:
        """异步初始化复合智能体实例"""
        try:
            # 1. 初始化模型
            await self._初始化模型()
            
            # 2. 加载工具
            await self._加载工具()
            
            # 3. 创建智能体执行器
            await self._创建智能体执行器()
            
            self.已初始化 = True
            复合智能体日志器.info(f"✅ 复合智能体实例初始化完成: {self.实例ID}")

        except Exception as e:
            复合智能体日志器.error(f"❌ 复合智能体实例初始化失败 {self.实例ID}: {str(e)}")
            raise

    async def _初始化模型(self) -> None:
        """初始化模型"""
        self.模型 = await LangChain模型管理器实例.获取模型(
            self.配置.模型名称,
            温度=self.配置.温度参数,
            最大令牌数=self.配置.最大令牌数,
            结构化输出模式=self.配置.自定义回复格式,
        )
        
        if not self.模型:
            raise Exception(f"模型初始化失败: {self.配置.模型名称}")

    async def _加载工具(self) -> None:
        """加载工具列表"""
        self.工具列表 = []
        
        if not self.配置.工具列表:
            return
            
        for 工具名称 in self.配置.工具列表:
            工具实例 = await self._获取工具实例(工具名称)
            if 工具实例:
                self.工具列表.append(工具实例)
                复合智能体日志器.info(f"✅ 加载工具: {工具名称}")
            else:
                复合智能体日志器.warning(f"⚠️ 工具加载失败: {工具名称}")

    async def _获取工具实例(self, 工具名称: str) -> Optional[Any]:
        """获取工具实例"""
        try:
            # 确保工具管理器已初始化
            if not LangChain工具管理器实例.已初始化:
                # 使用异步工厂方法创建实例
                await LangChain工具管理器实例._异步初始化()

            # 首先尝试从工具管理器的注册表获取
            if 工具名称 in LangChain工具管理器实例.工具注册表:
                工具实例 = LangChain工具管理器实例.工具注册表[工具名称]
                复合智能体日志器.debug(f"从工具管理器获取工具: {工具名称}")
                return 工具实例

            # 然后尝试从内部函数包装器获取
            if not 内部函数包装器实例.已初始化:
                await 内部函数包装器实例.初始化()

            if 工具名称 in 内部函数包装器实例.已注册工具:
                内部工具 = 内部函数包装器实例.已注册工具[工具名称]
                复合智能体日志器.debug(f"从内部函数包装器获取工具: {工具名称}")
                return 内部工具

            复合智能体日志器.warning(f"工具未找到: {工具名称}")
            return None

        except Exception as e:
            复合智能体日志器.error(f"获取工具实例失败 {工具名称}: {str(e)}")
            return None

    async def _创建智能体执行器(self) -> Optional[Any]:
        """创建智能体执行器"""
        try:
            if not self.工具列表:
                # 无工具的简单对话模型
                self.智能体执行器 = self.模型
                return

            # 创建ReAct提示词模板 - 优化版本，确保正确的格式输出
            react_prompt = PromptTemplate.from_template("""
{system_prompt}

你有以下工具可以使用：
{tools}

**重要：严格按照以下格式进行对话，每个步骤都必须完整输出：**

Question: 用户的问题
Thought: 你应该思考要做什么
Action: 要采取的行动，应该是 [{tool_names}] 中的一个
Action Input: 行动的输入
Observation: 行动的结果
... (这个 Thought/Action/Action Input/Observation 可以重复N次)
Thought: 我现在知道最终答案了，可以回复用户了
Final Answer: 对原始问题的最终答案

**格式要求：**
1. 每次工具调用后，必须等待Observation结果
2. 完成所有必要的工具调用后，必须输出"Thought: 我现在知道最终答案了"
3. 最后必须输出"Final Answer: "开头的最终回复
4. Final Answer应该是对用户友好的完整回复

开始！

Question: {input}
Thought: {agent_scratchpad}
""")

            # 创建ReAct智能体
            agent = create_react_agent(
                llm=self.模型,
                tools=self.工具列表,
                prompt=react_prompt
            )

            # 创建智能体执行器 - 优化配置以提高稳定性
            self.智能体执行器 = AgentExecutor(
                agent=agent,
                tools=self.工具列表,
                verbose=True,
                handle_parsing_errors="Check your output and make sure it conforms to the format instructions. Always end with 'Final Answer: ' followed by your response.",
                max_iterations=8,  # 适中的迭代次数
                max_execution_time=45,  # 适中的执行时间
                return_intermediate_steps=False,  # 不返回中间步骤，减少输出复杂度
                early_stopping_method="generate"  # 在生成最终答案后立即停止
            )

            复合智能体日志器.info(f"✅ 创建LangChain ReAct智能体: {len(self.工具列表)}个工具")

        except Exception as e:
            复合智能体日志器.error(f"创建智能体执行器失败: {str(e)}")
            # 回退到简单模型
            self.智能体执行器 = self.模型

    async def 处理对话(self, 用户消息: str, 会话id: str, 自定义变量: Optional[Dict[str, Any]] = None) -> str:
        """处理对话的核心方法 - 整合所有功能"""
        try:
            # 重置令牌消耗
            self.最后对话令牌消耗 = 0

            # 保存当前用户问题，用于结构化输出
            self._当前用户问题 = 用户消息

            # 初始化工具执行结果收集器
            self._工具执行结果 = {}

            # 0. 设置线程上下文参数（用于工具调用）
            self._设置线程上下文(自定义变量)

            # 1. RAG检索（如果启用）
            rag_上下文 = ""
            if self.rag_启用:
                rag_上下文 = await self._执行RAG检索(用户消息)

            # 2. 构建增强提示词
            增强提示词 = self._构建增强提示词(用户消息, rag_上下文, 自定义变量)

            # 3. 执行智能体对话并获取令牌消耗
            if isinstance(self.智能体执行器, AgentExecutor):
                # 使用工具智能体 - 通过回调获取令牌消耗
                response = await self.智能体执行器.ainvoke({
                    "input": 增强提示词,
                    "system_prompt": self.配置.系统提示词
                })
                原始回复 = response.get("output", "")

                # 保存完整的响应上下文，用于结构化输出处理
                self._当前响应上下文 = response

                # 收集工具执行结果
                self._收集工具执行结果(response)

                # 处理结构化输出 - 如果配置了JSON格式输出
                回复 = await self._处理结构化输出(原始回复)

                # TODO: 从AgentExecutor获取令牌消耗比较复杂，暂时使用估算
                self.最后对话令牌消耗 = len(增强提示词 + 回复) // 4
            else:
                # 使用简单模型 - 直接调用模型管理器获取令牌信息
                回复 = await self._调用模型并获取令牌消耗(增强提示词)

            # 4. 更新对话历史和令牌统计
            self._更新对话历史(用户消息, 回复)
            self.总令牌消耗 += self.最后对话令牌消耗

            return 回复

        except Exception as e:
            return 复合智能体错误处理器.处理对话错误(e, self.配置.智能体id or 0, 用户消息)

    async def _调用模型并获取令牌消耗(self, 提示词: str) -> str:
        """直接调用模型管理器并获取令牌消耗信息"""
        try:
            from 服务.LangChain_模型管理器 import LangChain模型管理器实例

            # 构建消息列表
            消息列表 = [{"role": "user", "content": 提示词}]
            if self.配置.系统提示词:
                消息列表.insert(0, {"role": "system", "content": self.配置.系统提示词})

            # 调用模型管理器获取响应和令牌信息
            模型响应 = await LangChain模型管理器实例.调用模型(
                模型名称=self.配置.模型名称,
                消息列表=消息列表,
                温度=self.配置.温度参数,
                最大令牌数=self.配置.最大令牌数
            )

            if 模型响应.get("success"):
                # 获取回复内容
                回复内容 = 模型响应.get("response", "")

                # 获取令牌消耗 - 优先使用API真实令牌数
                self.最后对话令牌消耗 = (
                    模型响应.get("API真实令牌总数") or
                    模型响应.get("代码估算令牌总数") or
                    0
                )

                复合智能体日志器.info(f"📊 模型调用成功，令牌消耗: {self.最后对话令牌消耗}")
                return 回复内容
            else:
                复合智能体日志器.error(f"❌ 模型调用失败: {模型响应.get('error')}")
                return "抱歉，模型调用失败，请稍后重试。"

        except Exception as e:
            复合智能体日志器.error(f"❌ 调用模型并获取令牌消耗失败: {str(e)}")
            return "抱歉，处理您的请求时出现错误。"

    async def _处理结构化输出(self, 原始回复: str) -> str:
        """处理结构化输出 - 使用LangChain官方with_structured_output方法"""
        try:
            if self.配置.输出格式 != "json" or not self.配置.自定义回复格式:
                return 原始回复

            复合智能体日志器.info("🔧 开始处理结构化输出")

            # 解析JSON Schema
            import json
            if isinstance(self.配置.自定义回复格式, str):
                json_schema = json.loads(self.配置.自定义回复格式)
            else:
                json_schema = self.配置.自定义回复格式

            # 修复JSON Schema格式错误
            json_schema = self._修复JSON_Schema(json_schema)

            # 使用LangChain官方with_structured_output方法
            json_输出 = await self._使用官方结构化输出(原始回复, json_schema)

            复合智能体日志器.info("✅ 结构化输出处理成功")
            return json_输出

        except Exception as e:
            复合智能体日志器.error(f"❌ 结构化输出处理失败: {str(e)}")
            return 原始回复

    def _修复JSON_Schema(self, json_schema: Dict[str, Any]) -> Dict[str, Any]:
        """修复JSON Schema格式错误"""
        try:
            # 修复properties中错误的required属性
            if "properties" in json_schema:
                for field_name, field_info in json_schema["properties"].items():
                    if isinstance(field_info, dict) and "required" in field_info:
                        # 移除字段内部的required属性
                        field_info.pop("required", None)
                        复合智能体日志器.info(f"🔧 修复字段 {field_name} 的required属性")

                        # 确保字段在根级别的required数组中
                        if "required" not in json_schema:
                            json_schema["required"] = []
                        if field_name not in json_schema["required"]:
                            json_schema["required"].append(field_name)
                            复合智能体日志器.info(f"✅ 添加 {field_name} 到required数组")

            return json_schema

        except Exception as e:
            复合智能体日志器.warning(f"⚠️ 修复JSON Schema失败: {str(e)}")
            return json_schema

    async def _使用官方结构化输出(self, 原始回复: str, json_schema: Dict[str, Any]) -> str:
        """使用LangChain官方with_structured_output方法 - 最优雅的实现"""
        try:
            from 服务.LangChain_模型管理器 import LangChain模型管理器实例
            from langchain_core.prompts import ChatPromptTemplate
            import json

            # 获取模型实例
            模型实例 = await LangChain模型管理器实例.获取模型(self.配置.模型名称)
            if not 模型实例:
                复合智能体日志器.warning("⚠️ 无法获取模型实例")
                return 原始回复

            # 构建工具执行上下文
            工具上下文 = self._构建工具执行上下文()

            # 创建结构化输出模型
            结构化模型 = 模型实例.with_structured_output(json_schema, method="json_mode")

            # 构建提示模板
            提示模板 = ChatPromptTemplate.from_messages([
                ("system", "你是一个专业的助手。请根据以下信息，严格按照JSON Schema格式生成回复。"),
                ("human", """请根据以下信息生成结构化的JSON回复：

用户问题: {用户问题}

智能体回复: {智能体回复}

{工具上下文}

JSON Schema:
{json_schema}

要求：
1. 严格按照Schema中定义的字段生成JSON
2. 根据字段描述智能填充相应的值
3. 如果字段描述提到了特定工具，请使用该工具的执行结果
4. 确保生成的JSON格式正确""")
            ])

            # 创建链
            链 = 提示模板 | 结构化模型

            # 调用链
            结果 = await 链.ainvoke({
                "用户问题": getattr(self, '_当前用户问题', '未知'),
                "智能体回复": 原始回复,
                "工具上下文": 工具上下文,
                "json_schema": json.dumps(json_schema, ensure_ascii=False, indent=2)
            })

            # 处理结果
            if isinstance(结果, dict):
                复合智能体日志器.info("✅ 官方结构化输出成功")
                return json.dumps(结果, ensure_ascii=False, indent=2)
            else:
                复合智能体日志器.warning(f"⚠️ 结果类型异常: {type(结果)}")
                return json.dumps({"error": "结构化输出格式异常"}, ensure_ascii=False, indent=2)

        except Exception as e:
            复合智能体日志器.error(f"❌ 官方结构化输出失败: {str(e)}")
            # 降级到简单JSON生成
            return json.dumps({
                "我的回答": 原始回复,
                "用户问题": getattr(self, '_当前用户问题', '未知')
            }, ensure_ascii=False, indent=2)

    def _构建工具执行上下文(self) -> str:
        """构建工具执行上下文信息"""
        try:
            if not hasattr(self, '_工具执行结果') or not self._工具执行结果:
                return ""

            工具上下文 = "\n工具执行结果："
            for 工具名, 结果 in self._工具执行结果.items():
                工具输出 = 结果.get('output', '')
                工具输入 = 结果.get('input', {})

                工具上下文 += f"\n- {工具名}: {工具输出}"
                if 工具输入:
                    工具上下文 += f"\n  输入参数: {工具输入}"

            return 工具上下文

        except Exception as e:
            复合智能体日志器.warning(f"⚠️ 构建工具执行上下文失败: {str(e)}")
            return ""














    def _收集工具执行结果(self, response: Dict[str, Any]) -> None:
        """收集工具执行结果到上下文中"""
        try:
            if 'intermediate_steps' not in response:
                return

            intermediate_steps = response['intermediate_steps']

            # 遍历所有工具执行步骤
            for step in intermediate_steps:
                if isinstance(step, tuple) and len(step) >= 2:
                    action, observation = step

                    if hasattr(action, 'tool'):
                        tool_name = action.tool
                        tool_input = getattr(action, 'tool_input', {})

                        # 保存工具执行结果
                        self._工具执行结果[tool_name] = {
                            'input': tool_input,
                            'output': str(observation) if observation else '',
                            'success': observation and "成功" in str(observation)
                        }

                        复合智能体日志器.debug(f"📦 收集工具结果: {tool_name} -> {str(observation)[:50]}...")

            复合智能体日志器.info(f"📦 工具执行结果收集完成: {len(self._工具执行结果)}个工具")

        except Exception as e:
            复合智能体日志器.warning(f"⚠️ 收集工具执行结果失败: {str(e)}")






    def _从工具结果匹配字段(self, field_name: str, description: str) -> Optional[str]:
        """根据字段描述匹配工具执行结果"""
        try:
            if not self._工具执行结果:
                return None

            # 1. 根据描述中提到的工具名称精确匹配
            for tool_name, tool_result in self._工具执行结果.items():
                if tool_name in description:
                    复合智能体日志器.info(f"🎯 精确匹配工具: {tool_name}")
                    return tool_result['output']

            # 2. 根据字段名称和描述的关键词匹配
            field_name_lower = field_name.lower()
            description_lower = description.lower()

            for tool_name, tool_result in self._工具执行结果.items():
                tool_name_lower = tool_name.lower()

                # 时间相关匹配
                if any(keyword in field_name_lower for keyword in ["时间", "time"]) and \
                   any(keyword in tool_name_lower for keyword in ["时间", "time"]):
                    # 优先从工具输入中提取时间
                    if isinstance(tool_result['input'], dict):
                        for key, value in tool_result['input'].items():
                            if "时间" in key and self._是有效时间格式(str(value)):
                                复合智能体日志器.info(f"🕒 从工具输入提取时间: {value}")
                                return str(value)

                    # 从工具输出中提取时间
                    import re
                    time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', tool_result['output'])
                    if time_match:
                        复合智能体日志器.info(f"🕒 从工具输出提取时间: {time_match.group(1)}")
                        return time_match.group(1)

                # 微信相关匹配
                if any(keyword in field_name_lower for keyword in ["微信", "沟通"]) and \
                   any(keyword in tool_name_lower for keyword in ["微信", "沟通"]):
                    return tool_result['output']

                # 通用关键词匹配
                if any(keyword in description_lower for keyword in ["工具", "执行", "结果"]):
                    return tool_result['output']

            # 3. 如果没有精确匹配，返回最后一个工具的输出
            if self._工具执行结果:
                last_tool_result = list(self._工具执行结果.values())[-1]
                return last_tool_result['output']

            return None

        except Exception as e:
            复合智能体日志器.warning(f"⚠️ 从工具结果匹配字段失败: {str(e)}")
            return None

    def _是有效时间格式(self, time_str: str) -> bool:
        """检查字符串是否为有效的时间格式"""
        try:
            import re
            # 检查常见的时间格式
            time_patterns = [
                r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',  # 2025-08-06 21:49:13
                r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',  # 2025-08-06T21:49:13
                r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}',  # 2025/08/06 21:49:13
                r'\d{2}:\d{2}:\d{2}',                     # 21:49:13
            ]

            for pattern in time_patterns:
                if re.match(pattern, str(time_str).strip()):
                    return True
            return False
        except Exception:
            return False

    def _检查工具执行状态(self) -> bool:
        """检查工具执行状态"""
        try:
            if not hasattr(self, '_当前响应上下文') or not self._当前响应上下文:
                return False

            response = self._当前响应上下文
            if 'intermediate_steps' not in response:
                return False

            intermediate_steps = response['intermediate_steps']

            # 检查是否有工具执行成功
            for step in intermediate_steps:
                if isinstance(step, tuple) and len(step) >= 2:
                    action, observation = step
                    if observation and "成功" in str(observation):
                        return True

            return len(intermediate_steps) > 0  # 有工具执行就认为是成功的

        except Exception as e:
            复合智能体日志器.warning(f"⚠️ 检查工具执行状态失败: {str(e)}")
            return False

    async def _执行RAG检索(self, 用户输入: str) -> str:
        """执行RAG检索获取知识库上下文 - 直接使用知识库服务"""
        try:
            if not self.配置.智能体id:
                return ""

            # 直接调用知识库服务的智能体RAG检索
            检索结果 = await LangChain知识库服务实例.智能体RAG检索(self.配置.智能体id, 用户输入)
            return 检索结果 or ""

        except Exception as e:
            return 复合智能体错误处理器.处理RAG检索错误(e, self.配置.智能体id or 0)

    def _构建增强提示词(self, 用户消息: str, rag_上下文: str, 自定义变量: Optional[Dict[str, Any]]) -> str:
        """构建增强的提示词"""
        try:
            基础提示词 = 用户消息

            # 添加RAG上下文
            if rag_上下文:
                基础提示词 = f"""
## 知识库上下文
以下是从知识库中检索到的相关信息，请基于这些信息回答用户问题：

{rag_上下文}

---

## 用户问题
{用户消息}
"""

            # 添加自定义变量
            if 自定义变量:
                变量部分 = "\n".join([f"{k}: {v}" for k, v in 自定义变量.items()])
                基础提示词 = f"""
## 自定义变量
{变量部分}

---

{基础提示词}
"""

            return 基础提示词

        except Exception as e:
            复合智能体日志器.error(f"构建增强提示词失败: {str(e)}")
            return 用户消息

    def _更新对话历史(self, 用户消息: str, ai回复: str):
        """更新对话历史"""
        try:
            self.对话历史.append({
                "type": "human",
                "content": 用户消息,
                "timestamp": datetime.now().isoformat()
            })
            self.对话历史.append({
                "type": "ai", 
                "content": ai回复,
                "timestamp": datetime.now().isoformat()
            })

            # 保持记忆窗口大小
            if len(self.对话历史) > self.配置.记忆窗口大小 * 2:
                self.对话历史 = self.对话历史[-self.配置.记忆窗口大小 * 2:]

        except Exception as e:
            复合智能体日志器.error(f"更新对话历史失败: {str(e)}")

    def _设置线程上下文(self, 自定义变量: Optional[Dict[str, Any]]):
        """设置线程上下文参数，用于工具调用时的参数注入"""
        try:
            # 导入内部函数包装器的上下文设置函数
            from 服务.LangChain_内部函数包装器 import 设置当前用户id, 设置当前智能体id, 设置当前自定义变量

            # 设置用户id
            设置当前用户id(self.配置.用户id)

            # 设置智能体id
            if self.配置.智能体id:
                设置当前智能体id(self.配置.智能体id)

            # 设置自定义变量
            if 自定义变量:
                设置当前自定义变量(自定义变量)
            else:
                # 不设置默认变量，让工具函数检查参数有效性
                设置当前自定义变量({})

            复合智能体日志器.debug(f"✅ 线程上下文设置完成: 用户id={self.配置.用户id}, 智能体id={self.配置.智能体id}")

        except Exception as e:
            复合智能体日志器.error(f"设置线程上下文失败: {str(e)}")


class 复合智能体错误处理器:
    """复合智能体错误处理器 - 简化版本"""

    @staticmethod
    def 处理对话错误(error: Exception, 智能体id: int, 用户消息: str) -> str:
        """处理对话错误"""
        try:
            error_message = str(error)
            复合智能体日志器.error(f"对话处理失败 - 智能体ID: {智能体id}, 错误: {error_message}")
            return f"对话处理失败: {error_message}"
        except Exception:
            return "对话处理失败: 未知错误"

    @staticmethod
    def 处理RAG检索错误(error: Exception, 智能体id: int) -> str:
        """处理RAG检索错误"""
        try:
            error_message = str(error)
            复合智能体日志器.error(f"RAG检索失败 - 智能体ID: {智能体id}, 错误: {error_message}")
            return ""  # RAG失败时返回空字符串，不影响对话
        except Exception:
            return ""

    @staticmethod
    def 处理工具调用错误(error: Exception, 工具名称: str) -> str:
        """处理工具调用错误"""
        try:
            error_message = str(error)
            复合智能体日志器.error(f"工具调用失败 - 工具: {工具名称}, 错误: {error_message}")
            return f"工具调用失败: {error_message}"
        except Exception:
            return "工具调用失败: 未知错误"


class LangChain复合智能体管理器:
    """复合智能体管理器 - 统一管理所有智能体实例"""

    def __init__(self):
        self.实例缓存: Dict[str, 复合智能体实例] = {}
        self.已初始化 = True
        self.最大缓存大小 = 100  # 防止内存泄漏
        self.缓存访问时间: Dict[str, datetime] = {}  # 跟踪访问时间
        复合智能体日志器.info("LangChain复合智能体管理器创建成功")

    async def 获取或创建实例(self, 智能体id: int, 用户id: int) -> 复合智能体实例:
        """获取或创建复合智能体实例"""
        try:
            实例ID = f"{智能体id}_{用户id}"
            
            # 检查缓存
            if 实例ID in self.实例缓存:
                self.缓存访问时间[实例ID] = datetime.now()  # 更新访问时间
                return self.实例缓存[实例ID]

            # 检查缓存大小，必要时清理
            await self._清理过期缓存()

            # 获取智能体配置
            智能体数据 = await LangChain智能体数据层实例.获取智能体详情完整(智能体id)
            if not 智能体数据:
                raise Exception(f"智能体不存在: {智能体id}")

            # 创建配置对象
            配置 = 复合智能体配置(
                用户id=用户id,
                智能体名称=智能体数据.get("智能体名称", ""),
                智能体id=智能体id,
                模型名称=智能体数据.get("模型名称", "qwen-turbo"),
                系统提示词=智能体数据.get("系统提示词", ""),
                温度参数=智能体数据.get("温度参数", 0.7),
                最大令牌数=智能体数据.get("最大令牌数", 4000),
                记忆窗口大小=智能体数据.get("记忆窗口大小", 10),
                启用rag=智能体数据.get("启用rag", False),
                输出格式=智能体数据.get("输出格式", "text"),
                自定义回复格式=智能体数据.get("自定义回复格式") if isinstance(智能体数据.get("自定义回复格式"), dict) else None,
                自定义变量=智能体数据.get("自定义变量") if isinstance(智能体数据.get("自定义变量"), list) else []
            )

            # 获取工具列表
            工具关联 = await LangChain智能体数据层实例.获取智能体工具关联(智能体id)
            配置.工具列表 = [工具["工具名称"] for 工具 in 工具关联] if 工具关联 else []

            # 创建新实例
            实例 = 复合智能体实例(配置, 实例ID)
            await 实例.初始化()
            
            # 缓存实例
            self.实例缓存[实例ID] = 实例
            self.缓存访问时间[实例ID] = datetime.now()

            复合智能体日志器.info(f"✅ 创建新复合智能体实例: {实例ID}")
            return 实例

        except Exception as e:
            复合智能体日志器.error(f"获取或创建实例失败 {智能体id}_{用户id}: {str(e)}")
            raise

    def 清理实例缓存(self):
        """清理实例缓存"""
        try:
            清理前数量 = len(self.实例缓存)
            self.实例缓存.clear()
            self.缓存访问时间.clear()
            复合智能体日志器.info(f"✅ 清理实例缓存完成: {清理前数量} -> 0")
        except Exception as e:
            复合智能体日志器.error(f"清理实例缓存失败: {str(e)}")

    async def _清理过期缓存(self) -> None:
        """清理过期的缓存实例，防止内存泄漏"""
        try:
            if len(self.实例缓存) <= self.最大缓存大小:
                return

            现在时间 = datetime.now()
            过期时间 = 3600  # 1小时未访问则过期

            # 找出过期的实例
            过期实例列表 = []
            for 实例ID, 访问时间 in self.缓存访问时间.items():
                if (现在时间 - 访问时间).total_seconds() > 过期时间:
                    过期实例列表.append(实例ID)

            # 清理过期实例
            for 实例ID in 过期实例列表:
                self.实例缓存.pop(实例ID, None)
                self.缓存访问时间.pop(实例ID, None)

            if 过期实例列表:
                复合智能体日志器.info(f"🧹 清理了 {len(过期实例列表)} 个过期缓存实例")

        except Exception as e:
            复合智能体日志器.error(f"清理缓存失败: {str(e)}")


# 创建全局实例
LangChain复合智能体管理器实例 = LangChain复合智能体管理器()
