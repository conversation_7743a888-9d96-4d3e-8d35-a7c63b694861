"""
寄样申请数据访问层
负责处理用户寄样信息表的数据访问操作

特性：
1. 寄样申请的创建和查询
2. 产品验证和权限检查
3. 分页查询和筛选
4. 与用户产品表和达人补充信息表的关联管理
"""

from typing import Any, Dict, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 接口日志器, 数据库日志器, 错误日志器


class 寄样申请数据访问:
    """寄样申请数据访问类"""

    @staticmethod
    async def 验证产品所有权(产品id: int, 用户id: int) -> bool:
        """
        验证产品是否属于当前用户

        Args:
            产品id: 产品id
            用户id: 用户id

        Returns:
            是否拥有权限
        """
        try:
            验证产品SQL = """
            SELECT id FROM 用户产品表
            WHERE id = $1 AND 用户id = $2 AND 状态 = 1
            LIMIT 1
            """

            产品验证结果 = await 异步连接池实例.执行查询(验证产品SQL, (产品id, 用户id))
            return len(产品验证结果) > 0

        except Exception as e:
            错误日志器.error(
                f"验证产品所有权失败: 产品id={产品id}, 用户id={用户id}, 错误={str(e)}"
            )
            return False

    @staticmethod
    async def 创建寄样申请(
        收件人: str,
        收件地址: str,
        收件电话: str,
        用户产品表id: int,
        数量: int,
        产品规格: str,
        申请备注: Optional[str],
        用户达人补充信息表id: int,
    ) -> Optional[int]:
        """
        创建寄样申请记录

        Args:
            收件人: 收件人姓名
            收件地址: 收件地址
            收件电话: 收件电话
            用户产品表id: 用户产品表id
            数量: 申请数量
            产品规格: 产品规格
            申请备注: 申请备注
            用户达人补充信息表id: 用户达人补充信息表id

        Returns:
            新创建的寄样申请ID或None
        """
        try:
            插入寄样申请SQL = """
            INSERT INTO 用户寄样信息表 (
                收件人, 地址, 电话, 用户产品表id, 数量, 规格, 寄样备注,
                用户审核状态, 团队审核状态, 用户达人补充信息表id, 创建时间, 更新时间
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
            RETURNING id
            """

            # 记录调试信息
            接口日志器.info(
                f"创建寄样申请 - 收件人: {收件人}, 产品规格: '{产品规格}', 数量: {数量}"
            )

            插入参数 = [
                收件人,
                收件地址,
                收件电话,
                用户产品表id,
                数量,
                产品规格,
                申请备注,
                0,  # 用户审核状态：0-待审批
                0,  # 团队审核状态：0-待审批
                用户达人补充信息表id,
            ]

            结果 = await 异步连接池实例.执行查询(插入寄样申请SQL, 插入参数)

            if 结果:
                寄样申请ID = 结果[0]["id"]
                数据库日志器.info(f"创建寄样申请成功: ID={寄样申请ID}")
                return 寄样申请ID
            else:
                return None

        except Exception as e:
            错误日志器.error(f"创建寄样申请失败: 收件人={收件人}, 错误={str(e)}")
            return None

    @staticmethod
    async def 查询寄样申请列表(
        用户id: int,
        页码: int = 1,
        每页数量: int = 20,
        状态筛选: Optional[str] = None,
        产品名称筛选: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        查询寄样申请列表

        Args:
            用户id: 用户id
            页码: 页码
            每页数量: 每页数量
            状态筛选: 状态筛选
            产品名称筛选: 产品名称筛选

        Returns:
            查询结果
        """
        try:
            # 构建查询条件
            where_conditions = [
                "s.用户达人补充信息表id IN (SELECT si.id FROM 用户达人补充信息表 si JOIN 用户达人关联表 ur ON si.用户达人关联表id = ur.id WHERE ur.用户id = $1)"
            ]
            参数列表 = [用户id]
            参数索引 = 2

            # 状态筛选
            if 状态筛选:
                if 状态筛选 == "待审批":
                    where_conditions.append("s.用户审核状态 = 0 AND s.团队审核状态 = 0")
                elif 状态筛选 == "已通过":
                    where_conditions.append("s.用户审核状态 = 1 AND s.团队审核状态 = 1")
                elif 状态筛选 == "已拒绝":
                    where_conditions.append(
                        "(s.用户审核状态 = 2 OR s.团队审核状态 = 2)"
                    )

            # 产品名称筛选
            if 产品名称筛选:
                where_conditions.append(f"p.产品名称 ILIKE ${参数索引}")
                参数列表.append(f"%{产品名称筛选}%")
                参数索引 += 1

            where_clause = " AND ".join(where_conditions)

            # 查询总数
            总数SQL = f"""
            SELECT COUNT(s.id) as 总数
            FROM 用户寄样信息表 s
            LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
            WHERE {where_clause}
            """

            总数结果 = await 异步连接池实例.执行查询(总数SQL, tuple(参数列表))
            总数 = 总数结果[0]["总数"] if 总数结果 else 0

            # 分页查询
            偏移量 = (页码 - 1) * 每页数量
            查询SQL = f"""
            SELECT
                s.id as 样品ID,
                s.收件人,
                s.地址,
                s.电话,
                s.数量,
                s.规格,
                s.寄样备注,
                s.用户审核状态,
                s.团队审核状态,
                s.创建时间,
                s.更新时间,
                p.产品名称,
                p.产品描述,
                p.产品图片
            FROM 用户寄样信息表 s
            LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
            WHERE {where_clause}
            ORDER BY s.创建时间 DESC
            LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """

            查询参数 = list(参数列表) + [每页数量, 偏移量]
            寄样记录 = await 异步连接池实例.执行查询(查询SQL, tuple(查询参数))

            return {"列表": 寄样记录, "总数": 总数, "页码": 页码, "每页数量": 每页数量}

        except Exception as e:
            错误日志器.error(f"查询寄样申请列表失败: 用户id={用户id}, 错误={str(e)}")
            return {"列表": [], "总数": 0, "页码": 页码, "每页数量": 每页数量}

    @staticmethod
    async def 获取寄样申请详情(样品ID: int, 用户id: int) -> Optional[Dict[str, Any]]:
        """
        获取寄样申请详情

        Args:
            样品ID: 样品ID
            用户id: 用户id

        Returns:
            寄样申请详情或None
        """
        try:
            查询SQL = """
            SELECT
                s.*,
                p.产品名称,
                p.产品描述,
                p.产品图片,
                si.联系方式,
                si.联系方式类型,
                ur.平台,
                ur.平台账号
            FROM 用户寄样信息表 s
            LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
            LEFT JOIN 用户达人补充信息表 si ON s.用户达人补充信息表id = si.id
            LEFT JOIN 用户达人关联表 ur ON si.用户达人关联表id = ur.id
            WHERE s.id = $1 AND ur.用户id = $2
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (样品ID, 用户id))

            if 结果:
                return 结果[0]
            else:
                return None

        except Exception as e:
            错误日志器.error(
                f"获取寄样申请详情失败: 样品ID={样品ID}, 用户id={用户id}, 错误={str(e)}"
            )
            return None

    @staticmethod
    async def 更新寄样申请状态(
        样品ID: int,
        用户审核状态: Optional[int] = None,
        团队审核状态: Optional[int] = None,
    ) -> bool:
        """
        更新寄样申请状态

        Args:
            样品ID: 样品ID
            用户审核状态: 用户审核状态
            团队审核状态: 团队审核状态

        Returns:
            是否更新成功
        """
        try:
            更新字段 = []
            参数列表 = []
            参数索引 = 1

            if 用户审核状态 is not None:
                更新字段.append(f"用户审核状态 = ${参数索引}")
                参数列表.append(用户审核状态)
                参数索引 += 1

            if 团队审核状态 is not None:
                更新字段.append(f"团队审核状态 = ${参数索引}")
                参数列表.append(团队审核状态)
                参数索引 += 1

            if not 更新字段:
                return True  # 没有要更新的字段

            更新字段.append("更新时间 = NOW()")
            参数列表.append(样品ID)

            更新SQL = f"""
            UPDATE 用户寄样信息表
            SET {", ".join(更新字段)}
            WHERE id = ${参数索引}
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)

            if 影响行数 > 0:
                数据库日志器.info(f"更新寄样申请状态成功: 样品ID={样品ID}")
                return True
            else:
                数据库日志器.warning(f"更新寄样申请状态未影响任何行: 样品ID={样品ID}")
                return False

        except Exception as e:
            错误日志器.error(f"更新寄样申请状态失败: 样品ID={样品ID}, 错误={str(e)}")
            return False


# 创建数据访问层实例
寄样申请数据访问实例 = 寄样申请数据访问()
