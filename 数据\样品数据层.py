"""
样品管理数据层
负责样品信息的数据库操作
"""

import logging
from typing import Dict, List, Optional, Tuple

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

logger = logging.getLogger(__name__)


class 样品数据层:
    """样品管理数据访问层"""

    async def 获取用户样品列表(
        self,
        用户id: int,
        页码: int = 1,
        每页数量: int = 10,
        收件人: Optional[str] = None,
        产品id: Optional[int] = None,
        审核状态: Optional[int] = None,
        快递单号: Optional[str] = None,
        快递状态: Optional[int] = None,
    ) -> Tuple[List[Dict], int]:
        """
        获取用户的样品列表（支持分页、搜索、筛选）

        参数:
            用户id: 用户id
            页码: 页码（从1开始）
            每页数量: 每页显示数量
            收件人: 收件人姓名查询条件
            产品id: 产品id查询条件
            审核状态: 审核状态筛选
            快递单号: 快递单号查询条件
            快递状态: 快递状态筛选

        返回:
            (样品列表, 总数)
        """
        try:
            # 构建查询条件
            条件列表 = []
            参数列表 = []

            # 正确的逻辑：通过微信产品对接进度表的用户id来过滤
            条件列表.append("wp.用户id = $1")
            参数列表.append(用户id)
            参数索引 = 2

            if 收件人:
                条件列表.append(f"s.收件人 LIKE ${参数索引}")
                参数列表.append(f"%{收件人}%")
                参数索引 += 1

            if 产品id:
                条件列表.append(f"s.用户产品表id = ${参数索引}")
                参数列表.append(产品id)
                参数索引 += 1

            if 审核状态 is not None:
                # 根据审核状态参数决定查询哪个审核状态字段
                # 0: 待审核, 1: 已审核, 2: 审核拒绝
                条件列表.append(
                    f"(s.用户审核状态 = ${参数索引} OR s.团队审核状态 = ${参数索引 + 1})"
                )
                参数列表.extend([审核状态, 审核状态])
                参数索引 += 2

            if 快递单号:
                条件列表.append(f"s.快递单号 LIKE ${参数索引}")
                参数列表.append(f"%{快递单号}%")
                参数索引 += 1

            if 快递状态 is not None:
                条件列表.append(f"s.快递状态 = ${参数索引}")
                参数列表.append(快递状态)
                参数索引 += 1

            # 构建WHERE子句
            WHERE子句 = " AND ".join(条件列表) if 条件列表 else "1=1"

            # 查询总数（支持微信寄样和达人寄样两种类型）
            总数SQL = f"""
                SELECT COUNT(s.id) as 总数
                FROM 用户寄样信息表 s
                LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
                LEFT JOIN 微信产品对接进度表 wp ON s.微信产品对接进度表id = wp.id
                LEFT JOIN 用户达人补充信息表 ui ON s.用户达人补充信息表id = ui.id
                WHERE {WHERE子句}
            """

            总数结果 = await 异步连接池实例.执行查询(总数SQL, 参数列表)
            总记录数 = 总数结果[0]["总数"] if 总数结果 else 0

            # 计算分页参数
            偏移量 = (页码 - 1) * 每页数量

            # 查询分页数据（支持微信寄样和达人寄样两种类型）
            查询SQL = f"""
                SELECT s.*,
                       p.产品名称, p.产品分类, p.产品描述, p.产品信息,
                       -- 微信寄样相关信息
                       wp.我方微信号id, wi_me.微信号 AS 我方微信号,
                       wp.用户联系人表id, wi_other.微信号 AS 对方微信号,
                       -- 达人寄样相关信息
                       ui.id as 达人补充信息id,
                       ur.达人id,
                       k.昵称 as 达人昵称,
                       k.account_douyin as 达人抖音号,
                       k.avatar as 达人头像,
                       ui.联系方式 as 达人联系方式,
                       ui.个人备注 as 达人个人备注,
                       -- 寄样类型标识
                       CASE
                           WHEN s.微信产品对接进度表id IS NOT NULL THEN '微信寄样'
                           WHEN s.用户达人补充信息表id IS NOT NULL THEN '达人寄样'
                           ELSE '未知类型'
                       END as 寄样类型
                FROM 用户寄样信息表 s
                LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
                -- 微信寄样关联
                LEFT JOIN 微信产品对接进度表 wp ON s.微信产品对接进度表id = wp.id
                LEFT JOIN 微信信息表 wi_me ON wp.我方微信号id = wi_me.id
                LEFT JOIN 用户联系人表 uc_wx ON wp.用户联系人表id = uc_wx.用户联系人id
                LEFT JOIN 用户达人补充信息表 usi_wx ON uc_wx.用户联系人id = usi_wx.用户联系人表id
                LEFT JOIN 微信信息表 wi_other ON usi_wx.微信信息表id = wi_other.id
                -- 达人寄样关联
                LEFT JOIN 用户达人补充信息表 ui ON s.用户达人补充信息表id = ui.id
                LEFT JOIN 用户达人关联表 ur ON ui.用户达人关联表id = ur.id
                LEFT JOIN 达人表 k ON ur.达人id = k.id
                WHERE {WHERE子句}
                ORDER BY s.创建时间 DESC
                LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """

            查询参数 = 参数列表 + [每页数量, 偏移量]
            样品列表 = await 异步连接池实例.执行查询(查询SQL, 查询参数)

            logger.info(
                f"获取用户样品列表成功: 用户id={用户id}, 总数={总记录数}, 当前页={页码}"
            )
            return 样品列表, 总记录数
        except Exception as e:
            logger.error(f"获取用户样品列表失败: {e}")
            raise

    async def 获取样品详情(self, 样品id: int) -> Optional[Dict]:
        """
        获取样品详细信息

        参数:
            样品id: 样品id

        返回:
            样品详细信息
        """
        try:
            查询SQL = """
                SELECT s.*,
                       p.产品名称, p.产品分类, p.产品描述, p.产品信息,
                       -- 微信寄样相关信息
                       wp.我方微信号id, wi_me.微信号 AS 我方微信号,
                       wp.对方微信号id, wi_other.微信号 AS 对方微信号,
                       -- 达人寄样相关信息
                       ui.id as 达人补充信息id,
                       ur.达人id,
                       k.昵称 as 达人昵称,
                       k.account_douyin as 达人抖音号,
                       k.avatar as 达人头像,
                       ui.联系方式 as 达人联系方式,
                       ui.个人备注 as 达人个人备注,
                       -- 寄样类型标识
                       CASE
                           WHEN s.微信产品对接进度表id IS NOT NULL THEN '微信寄样'
                           WHEN s.用户达人补充信息表id IS NOT NULL THEN '达人寄样'
                           ELSE '未知类型'
                       END as 寄样类型
                FROM 用户寄样信息表 s
                LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
                -- 微信寄样关联
                LEFT JOIN 微信产品对接进度表 wp ON s.微信产品对接进度表id = wp.id
                LEFT JOIN 微信信息表 wi_me ON wp.我方微信号id = wi_me.id
                LEFT JOIN 微信信息表 wi_other ON wp.对方微信号id = wi_other.id
                -- 达人寄样关联
                LEFT JOIN 用户达人补充信息表 ui ON s.用户达人补充信息表id = ui.id
                LEFT JOIN 用户达人关联表 ur ON ui.用户达人关联表id = ur.id
                LEFT JOIN 达人表 k ON ur.达人id = k.id
                WHERE s.id = $1
            """

            结果列表 = await 异步连接池实例.执行查询(查询SQL, (样品id,))

            if 结果列表:
                logger.info(f"获取样品详情成功: 样品id={样品id}")
                return 结果列表[0]
            else:
                logger.warning(f"样品不存在: 样品id={样品id}")
                return None

        except Exception as e:
            logger.error(f"获取样品详情失败: {e}")
            raise

    async def 获取样品统计信息(self, 用户id: int) -> Dict:
        """
        获取用户的样品统计信息

        参数:
            用户id: 用户id

        返回:
            统计信息字典
        """
        try:
            统计SQL = """
                SELECT
                    COUNT(*) as 总数,
                    COUNT(CASE WHEN (s.用户审核状态 = 0 OR s.团队审核状态 = 0) THEN 1 END) as 待审核,
                    COUNT(CASE WHEN (s.用户审核状态 = 1 OR s.团队审核状态 = 1) THEN 1 END) as 已审核,
                    COUNT(CASE WHEN (s.用户审核状态 = 2 OR s.团队审核状态 = 2) THEN 1 END) as 审核拒绝,
                    COUNT(CASE WHEN s.快递状态 = 0 THEN 1 END) as 待发货,
                    COUNT(CASE WHEN s.快递状态 = 1 THEN 1 END) as 已发货,
                    COUNT(CASE WHEN s.快递状态 = 2 THEN 1 END) as 已签收,
                    MAX(s.创建时间) as 最近申请时间
                FROM 用户寄样信息表 s
                JOIN 微信产品对接进度表 wp ON s.微信产品对接进度表id = wp.id
                WHERE wp.用户id = $1
            """

            结果列表 = await 异步连接池实例.执行查询(统计SQL, (用户id,))

            if 结果列表:
                统计信息 = 结果列表[0]
                logger.info(f"获取样品统计信息成功: 用户id={用户id}")
                return 统计信息
            else:
                return {
                    "总数": 0,
                    "待审核": 0,
                    "已审核": 0,
                    "审核拒绝": 0,
                    "待发货": 0,
                    "已发货": 0,
                    "已签收": 0,
                    "最近申请时间": None,
                }

        except Exception as e:
            logger.error(f"获取样品统计信息失败: {e}")
            raise

    async def 更新样品审核状态(
        self,
        样品id: int,
        用户审核状态: Optional[int] = None,
        团队审核状态: Optional[int] = None,
    ) -> bool:
        """
        更新样品审核状态

        参数:
            样品id: 样品id
            用户审核状态: 用户审核状态 (可选)
            团队审核状态: 团队审核状态 (可选)

        返回:
            是否更新成功
        """
        try:
            更新字段 = []
            参数列表 = []
            参数索引 = 1

            if 用户审核状态 is not None:
                更新字段.append(f"用户审核状态 = ${参数索引}")
                参数列表.append(用户审核状态)
                参数索引 += 1

            if 团队审核状态 is not None:
                更新字段.append(f"团队审核状态 = ${参数索引}")
                参数列表.append(团队审核状态)
                参数索引 += 1

            if not 更新字段:
                logger.warning("没有提供需要更新的审核状态")
                return False

            更新字段.append("更新时间 = NOW()")
            参数列表.append(样品id)

            更新SQL = f"""
                UPDATE 用户寄样信息表
                SET {", ".join(更新字段)}
                WHERE id = ${参数索引}
            """

            影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)

            if 影响行数 > 0:
                logger.info(
                    f"更新样品审核状态成功: 样品id={样品id}, 用户审核状态={用户审核状态}, 团队审核状态={团队审核状态}"
                )
                return True
            else:
                logger.warning(f"更新样品审核状态失败，样品不存在: 样品id={样品id}")
                return False

        except Exception as e:
            logger.error(f"更新样品审核状态失败: {e}")
            raise

    async def 更新样品快递信息(self, 样品id: int, 快递信息: Dict) -> bool:
        """
        更新样品快递信息

        参数:
            样品id: 样品id
            快递信息: 快递信息字典

        返回:
            是否更新成功
        """
        try:
            更新字段 = []
            参数列表 = []
            参数索引 = 1

            if "快递单号" in 快递信息:
                更新字段.append(f"快递单号 = ${参数索引}")
                参数列表.append(快递信息["快递单号"])
                参数索引 += 1

            if "快递状态" in 快递信息:
                更新字段.append(f"快递状态 = ${参数索引}")
                参数列表.append(快递信息["快递状态"])
                参数索引 += 1

            if "快递状态描述" in 快递信息:
                更新字段.append(f"快递状态描述 = ${参数索引}")
                参数列表.append(快递信息["快递状态描述"])
                参数索引 += 1

            if "快递公司" in 快递信息:
                更新字段.append(f"快递公司 = ${参数索引}")
                参数列表.append(快递信息["快递公司"])
                参数索引 += 1

            if "物流跟踪详情" in 快递信息:
                更新字段.append(f"物流跟踪详情 = ${参数索引}")
                参数列表.append(快递信息["物流跟踪详情"])
                参数索引 += 1

            if 更新字段:
                更新字段.append("快递状态变更时间 = NOW()")
                参数列表.append(样品id)

                更新SQL = f"""
                    UPDATE 用户寄样信息表
                    SET {", ".join(更新字段)}
                    WHERE id = ${参数索引}
                """

                影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)

                if 影响行数 > 0:
                    logger.info(f"更新样品快递信息成功: 样品id={样品id}")
                    return True
                else:
                    logger.warning(f"更新样品快递信息失败，样品不存在: 样品id={样品id}")
                    return False

            return False

        except Exception as e:
            logger.error(f"更新样品快递信息失败: {e}")
            raise


# 创建数据层实例
样品数据层实例 = 样品数据层()
