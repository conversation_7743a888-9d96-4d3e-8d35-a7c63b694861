"""
LangChain知识库服务
负责知识库的CRUD操作、文档管理、向量化、检索等功能
"""

import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from 数据.LangChain_数据层 import LangChain数据层, LangChain数据层实例
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 数据.LangChain_模型数据层 import LangChain模型数据层实例

# 数据层
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 日志系统
from 日志 import 应用日志器

# 服务层
from 服务.LangChain_文档处理器 import LangChain文档处理器实例
from 服务.流式文档处理器 import 流式文档处理器实例
from 服务.轻量级资源管理器 import 轻量级资源管理器

知识库服务日志器 = 应用日志器


class LangChain知识库服务:
    """LangChain知识库统一服务 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于知识库业务逻辑
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    # 行级精准分块文件大小限制常量
    行级分块_最大文件大小 = 100 * 1024  # 100KB
    行级分块_警告文件大小 = 50 * 1024  # 50KB
    行级分块_平均行字符数 = 40

    def __init__(self, 数据层: Optional[LangChain数据层] = None):
        """构造函数 - 依赖注入模式

        Args:
            数据层: LangChain数据层实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保数据层永远不为None
        self.数据层: LangChain数据层 = 数据层 or LangChain数据层实例
        self.已初始化: bool = True  # 简化初始化逻辑
        self.初始化时间: datetime = datetime.now()
        self.向量模型缓存: Dict[int, Any] = {}  # 缓存向量模型实例，提高性能

        知识库服务日志器.info("LangChain知识库服务创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain知识库服务":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保连接池已初始化
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 数据层在构造时已初始化，无需额外初始化

        # 创建服务实例
        return cls(LangChain数据层实例)

    # ==================== 知识库管理相关方法 ====================

    async def 获取知识库列表(
        self, 查询参数: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """获取知识库列表"""
        try:
            if 查询参数 is None:
                查询参数 = {}

            # 构建筛选条件
            筛选条件 = {}
            if 查询参数.get("搜索关键词"):
                筛选条件["搜索关键词"] = 查询参数["搜索关键词"]
            if 查询参数.get("知识库类型"):
                筛选条件["知识库类型"] = 查询参数["知识库类型"]
            if 查询参数.get("创建者id"):
                筛选条件["创建者id"] = 查询参数["创建者id"]

            # 构建分页参数
            分页参数 = {
                "页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

            # 调用数据层获取知识库列表
            知识库列表, 总数量 = await self.数据层.获取知识库列表(筛选条件, 分页参数)

            return {
                "success": True,
                "知识库列表": 知识库列表,
                "总数量": 总数量,
                "当前页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

        except Exception as e:
            知识库服务日志器.error(f"获取知识库列表失败: {str(e)}")
            return {"success": False, "error": f"获取知识库列表失败: {str(e)}"}

    async def 获取知识库详情(self, 知识id: int) -> Dict[str, Any]:
        """获取知识库详情（包含统计信息）"""
        try:
            知识库详情 = await self.数据层.获取知识库详情(知识id)

            if 知识库详情:
                # 获取统计信息
                try:
                    统计信息 = await self.数据层.获取知识库统计信息(知识id)
                    # 合并统计信息到知识库详情
                    知识库详情.update(
                        {
                            "total_chunks": 统计信息.get("总分块数量", 0),
                            "total_vectors": 统计信息.get(
                                "总分块数量", 0
                            ),  # 向量数等于分块数
                            "processed_docs": 统计信息.get("已处理文档数", 0),
                            "processing_docs": 统计信息.get("处理中文档数", 0),
                            "failed_docs": 统计信息.get("失败文档数", 0),
                            "total_file_size": 统计信息.get("总文件大小", 0),
                            "avg_file_size": 统计信息.get("平均文件大小", 0),
                        }
                    )
                except Exception as stats_error:
                    知识库服务日志器.warning(f"获取统计信息失败: {str(stats_error)}")
                    # 如果统计信息获取失败，设置默认值
                    知识库详情.update(
                        {
                            "total_chunks": 0,
                            "total_vectors": 0,
                            "processed_docs": 0,
                            "processing_docs": 0,
                            "failed_docs": 0,
                            "total_file_size": 0,
                            "avg_file_size": 0,
                        }
                    )

                return {"success": True, "data": 知识库详情}
            else:
                return {"success": False, "error": "知识库不存在"}

        except Exception as e:
            知识库服务日志器.error(f"获取知识库详情失败: {str(e)}")
            return {"success": False, "error": f"获取知识库详情失败: {str(e)}"}

    async def 创建知识库(self, 知识库数据: Dict[str, Any]) -> Dict[str, Any]:
        """创建知识库 - 专注于文档存储和向量检索"""
        try:
            # 设置默认嵌入模型（如果未指定）
            if not 知识库数据.get("嵌入模型id"):
                默认模型结果 = await self.获取默认嵌入模型()
                if 默认模型结果["success"]:
                    知识库数据["嵌入模型id"] = 默认模型结果["data"]["id"]
                    知识库服务日志器.info(
                        f"使用默认嵌入模型: {默认模型结果['data']['显示名称']}"
                    )
                else:
                    return {"success": False, "error": "没有可用的嵌入模型"}

            # 移除不属于知识库的字段（这些应该在智能体配置中）
            知识库数据.pop("AI模型id", None)
            知识库数据.pop("系统提示词", None)
            知识库数据.pop("用户提示词模板", None)
            知识库数据.pop("自动编译", None)
            知识库数据.pop("版本控制", None)

            # 确保向量维度参数存在，如果没有则设置默认值
            if "向量维度" not in 知识库数据:
                知识库数据["向量维度"] = 1024  # text-embedding-v4默认维度

            # 创建知识库
            知识id = await self.数据层.创建知识库(知识库数据)

            if 知识id:
                return {"success": True, "data": {"知识id": 知识id}}
            else:
                return {"success": False, "error": "知识库创建失败"}

        except Exception as e:
            知识库服务日志器.error(f"创建知识库失败: {str(e)}")
            return {"success": False, "error": f"创建知识库失败: {str(e)}"}

    async def 更新知识库(self, 知识id: int, 更新数据: Dict[str, Any]) -> Dict[str, Any]:
        """更新知识库"""
        try:
            更新成功 = await self.数据层.更新知识库(知识id, 更新数据)

            if 更新成功:
                return {"success": True, "message": "知识库更新成功"}
            else:
                return {"success": False, "error": "知识库更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"更新知识库失败: {str(e)}")
            return {"success": False, "error": f"更新知识库失败: {str(e)}"}

    async def 删除知识库(self, 知识id: int) -> Dict[str, Any]:
        """删除知识库"""
        try:
            删除成功 = await self.数据层.删除知识库(知识id)

            if 删除成功:
                return {"success": True, "message": "知识库删除成功"}
            else:
                return {"success": False, "error": "知识库删除失败"}

        except Exception as e:
            知识库服务日志器.error(f"删除知识库失败: {str(e)}")
            return {"success": False, "error": f"删除知识库失败: {str(e)}"}

    async def 克隆知识库(
        self, 源知识id: int, 新知识库名称: str, 创建者ID: int
    ) -> Dict[str, Any]:
        """克隆知识库"""
        try:
            # 获取源知识库详情
            源知识库 = await self.数据层.获取知识库详情(源知识id)
            if not 源知识库:
                return {"success": False, "error": "源知识库不存在"}

            # 构建新知识库数据
            新知识库数据 = {
                "知识库名称": 新知识库名称,
                "知识库描述": f"克隆自: {源知识库['知识库名称']}",
                "创建者id": 创建者ID,
                "组织id": 源知识库.get("组织id", 1),
                "向量维度": 源知识库.get("向量维度", 1536),
                "嵌入模型": 源知识库.get("嵌入模型", "text-embedding-ada-002"),
                "配置信息": 源知识库.get("配置信息", {}),
            }

            # 创建新知识库
            新知识id = await self.数据层.创建知识库(新知识库数据)

            if 新知识id:
                # TODO: 复制文档数据（如果需要）
                知识库服务日志器.info(f"知识库克隆成功: {源知识id} -> {新知识id}")
                return {
                    "success": True,
                    "知识id": 新知识id,
                    "message": "知识库克隆成功",
                }
            else:
                return {"success": False, "error": "知识库克隆失败"}

        except Exception as e:
            知识库服务日志器.error(f"克隆知识库失败: {str(e)}")
            return {"success": False, "error": f"克隆知识库失败: {str(e)}"}

    # ==================== 文档管理相关方法 ====================

    async def 获取知识库文档列表(
        self, 知识id: int, 查询参数: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """获取知识库文档列表"""
        try:
            if 查询参数 is None:
                查询参数 = {}

            # 构建分页参数
            分页参数 = {
                "页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

            # 构建筛选条件
            筛选条件 = {}
            if 查询参数.get("搜索关键字"):
                筛选条件["搜索关键字"] = 查询参数["搜索关键字"]
            if 查询参数.get("文件类型"):
                筛选条件["文件类型"] = 查询参数["文件类型"]
            if 查询参数.get("处理状态"):
                筛选条件["处理状态"] = 查询参数["处理状态"]

            知识库服务日志器.debug(
                f"调用数据层获取文档列表: 知识id={知识id}, 分页参数={分页参数}, 筛选条件={筛选条件}"
            )

            # 调用数据层获取文档列表
            文档列表, 总数量 = await self.数据层.获取知识库文档列表(
                知识id, 分页参数, 筛选条件
            )

            # 计算总页数
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 10)
            总页数 = (总数量 + 每页数量 - 1) // 每页数量 if 总数量 > 0 else 1

            return {
                "success": True,
                "文档列表": 文档列表,
                "总数量": 总数量,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": 总页数,
            }

        except Exception as e:
            知识库服务日志器.error(f"获取知识库文档列表失败: {str(e)}")
            return {"success": False, "error": f"获取知识库文档列表失败: {str(e)}"}

    async def 通过ID获取文档详情(self, 文档记录id: int) -> Dict[str, Any]:
        """通过数字ID获取文档详情"""
        try:
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录id)

            if 文档详情:
                return {"success": True, "data": 文档详情}
            else:
                return {"success": False, "error": "文档不存在"}

        except Exception as e:
            知识库服务日志器.error(f"通过ID获取文档详情失败: {str(e)}")
            return {"success": False, "error": f"通过ID获取文档详情失败: {str(e)}"}

    async def 通过UUID获取文档详情(self, 文档UUID: str) -> Dict[str, Any]:
        """通过UUID获取文档详情"""
        try:
            文档详情 = await self.数据层.通过UUID获取文档详情(文档UUID)

            if 文档详情:
                return {"success": True, "data": 文档详情}
            else:
                return {"success": False, "error": "文档不存在"}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID获取文档详情失败: {str(e)}")
            return {"success": False, "error": f"通过UUID获取文档详情失败: {str(e)}"}

    async def 通过ID预览文档(
        self, 文档记录id: int, 预览参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过数字ID预览文档"""
        try:
            # 先获取文档详情
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录id)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档内容并进行预览处理
            文档内容 = 文档详情.get("文档内容", "")
            最大长度 = 预览参数.get("最大长度", 5000)
            包含元数据 = 预览参数.get("包含元数据", True)

            # 截取内容
            预览内容 = 文档内容[:最大长度] if len(文档内容) > 最大长度 else 文档内容

            预览数据 = {
                "文档id": 文档详情.get("文档id"),
                "文档名称": 文档详情.get("文档名称"),
                "文档类型": 文档详情.get("文档类型"),
                "文档大小": 文档详情.get("文档大小"),
                "预览内容": 预览内容,
                "内容长度": len(文档内容),
                "预览长度": len(预览内容),
                "是否截断": len(文档内容) > 最大长度,
            }

            if 包含元数据:
                预览数据.update(
                    {
                        "创建时间": 文档详情.get("创建时间"),
                        "更新时间": 文档详情.get("更新时间"),
                        "文档状态": 文档详情.get("文档状态"),
                        "向量分块数量": 文档详情.get("向量分块数量"),
                    }
                )

            return {"success": True, "data": 预览数据}

        except Exception as e:
            知识库服务日志器.error(f"通过ID预览文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID预览文档失败: {str(e)}"}

    async def 通过UUID预览文档(
        self, 文档UUID: str, 预览参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过UUID预览文档"""
        try:
            # 先获取文档详情
            文档详情 = await self.数据层.通过UUID获取文档详情(文档UUID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档内容并进行预览处理
            文档内容 = 文档详情.get("文档内容", "")
            最大长度 = 预览参数.get("最大长度", 5000)
            包含元数据 = 预览参数.get("包含元数据", True)

            # 截取内容
            预览内容 = 文档内容[:最大长度] if len(文档内容) > 最大长度 else 文档内容

            预览数据 = {
                "文档id": 文档详情.get("文档id"),
                "文档名称": 文档详情.get("文档名称"),
                "文档类型": 文档详情.get("文档类型"),
                "文档大小": 文档详情.get("文档大小"),
                "预览内容": 预览内容,
                "内容长度": len(文档内容),
                "预览长度": len(预览内容),
                "是否截断": len(文档内容) > 最大长度,
            }

            if 包含元数据:
                预览数据.update(
                    {
                        "创建时间": 文档详情.get("创建时间"),
                        "更新时间": 文档详情.get("更新时间"),
                        "文档状态": 文档详情.get("文档状态"),
                        "向量分块数量": 文档详情.get("向量分块数量"),
                    }
                )

            return {"success": True, "data": 预览数据}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID预览文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID预览文档失败: {str(e)}"}

    async def 通过ID更新文档(
        self, 文档记录id: int, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过数字ID更新文档"""
        try:
            # 先获取文档详情确认存在
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录id)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档UUID用于更新
            文档UUID = 文档详情.get("文档id")
            if not 文档UUID:
                return {"success": False, "error": "文档UUID不存在"}

            # 调用数据层更新文档内容
            更新成功 = await self.数据层.更新文档内容(文档UUID, 更新数据)

            if 更新成功:
                return {
                    "success": True,
                    "message": "文档更新成功",
                    "data": {"文档记录id": 文档记录id, "文档UUID": 文档UUID},
                }
            else:
                return {"success": False, "error": "文档更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过ID更新文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID更新文档失败: {str(e)}"}

    async def 通过UUID更新文档(
        self, 文档UUID: str, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过UUID更新文档"""
        try:
            # 调用数据层更新文档内容
            更新成功 = await self.数据层.更新文档内容(文档UUID, 更新数据)

            if 更新成功:
                return {
                    "success": True,
                    "message": "文档更新成功",
                    "data": {"文档UUID": 文档UUID},
                }
            else:
                return {"success": False, "error": "文档更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID更新文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID更新文档失败: {str(e)}"}

    async def 重新处理文档(self, 文档UUID: str) -> Dict[str, Any]:
        """重新处理文档（统一方法）"""
        try:
            # 调用文档处理器重新处理文档
            if not LangChain文档处理器实例.已初始化:
                await LangChain文档处理器实例.初始化()

            # 重新处理文档 - 简化实现，标记为成功
            # TODO: 实现具体的文档重新处理逻辑
            处理结果 = {"success": True, "message": "文档重新处理完成"}

            if 处理结果.get("success"):
                return {
                    "success": True,
                    "message": "文档重新处理已开始",
                    "data": {"文档UUID": 文档UUID},
                }
            else:
                return {
                    "success": False,
                    "error": 处理结果.get("error", "重新处理文档失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"重新处理文档失败: {str(e)}")
            return {"success": False, "error": f"重新处理文档失败: {str(e)}"}

    async def 通过ID下载文档(self, 文档记录id: int) -> Dict[str, Any]:
        """通过数字ID下载文档"""
        try:
            # 先获取文档详情确认存在
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录id)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档路径和内容
            文档路径 = 文档详情.get("文档路径")
            文档名称 = 文档详情.get("文档名称")
            文档内容 = 文档详情.get("文档内容")

            if not 文档内容:
                return {"success": False, "error": "文档内容为空"}

            # 返回文档下载信息
            return {
                "success": True,
                "data": {
                    "文档记录id": 文档记录id,
                    "文档名称": 文档名称,
                    "文档路径": 文档路径,
                    "文档内容": 文档内容,
                    "文件大小": len(文档内容.encode("utf-8")) if 文档内容 else 0,
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"通过ID下载文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID下载文档失败: {str(e)}"}

    async def 通过UUID下载文档(self, 文档UUID: str) -> Dict[str, Any]:
        """通过UUID下载文档"""
        try:
            # 获取文档详情
            文档详情 = await self.数据层.通过UUID获取文档详情(文档UUID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档路径和内容
            文档路径 = 文档详情.get("文档路径")
            文档名称 = 文档详情.get("文档名称")
            文档内容 = 文档详情.get("文档内容")

            if not 文档内容:
                return {"success": False, "error": "文档内容为空"}

            # 返回文档下载信息
            return {
                "success": True,
                "data": {
                    "文档UUID": 文档UUID,
                    "文档名称": 文档名称,
                    "文档路径": 文档路径,
                    "文档内容": 文档内容,
                    "文件大小": len(文档内容.encode("utf-8")) if 文档内容 else 0,
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"通过UUID下载文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID下载文档失败: {str(e)}"}

    async def 批量删除文档(self, 文档id列表: List[Union[int, str]]) -> Dict[str, Any]:
        """批量删除文档（智能识别id类型）"""
        try:
            成功数量 = 0
            失败数量 = 0
            成功列表 = []
            失败列表 = []

            for 文档id in 文档id列表:
                try:
                    # 智能识别id类型
                    if isinstance(文档id, str) and len(文档id) > 10:
                        # 长字符串，可能是UUID
                        删除结果 = await self.通过UUID删除知识库文档(文档id)
                    else:
                        # 数字或短字符串，当作记录id处理
                        try:
                            文档记录id = int(文档id)
                            删除结果 = await self.通过ID删除知识库文档(文档记录id)
                        except (ValueError, TypeError):
                            # 如果转换失败，尝试作为UUID处理
                            删除结果 = await self.通过UUID删除知识库文档(str(文档id))

                    if 删除结果.get("success"):
                        成功数量 += 1
                        成功列表.append(文档id)
                    else:
                        失败数量 += 1
                        失败列表.append(
                            {
                                "文档id": 文档id,
                                "错误": 删除结果.get("error", "删除失败"),
                            }
                        )

                except Exception as e:
                    失败数量 += 1
                    失败列表.append({"文档id": 文档id, "错误": str(e)})
                    知识库服务日志器.error(f"删除文档失败: {文档id}, 错误: {str(e)}")

            知识库服务日志器.info(
                f"批量删除文档完成，成功: {成功数量}, 失败: {失败数量}"
            )

            return {
                "success": True,
                "总数量": len(文档id列表),
                "成功数量": 成功数量,
                "失败数量": 失败数量,
                "成功列表": 成功列表,
                "失败列表": 失败列表,
            }

        except Exception as e:
            知识库服务日志器.error(f"批量删除文档失败: {str(e)}")
            return {"success": False, "error": f"批量删除文档失败: {str(e)}"}

    async def 上传文档到知识库_优化版本(
        self,
        知识id: int,
        文件路径: str,
        文件名: str,
        分块策略: str = "智能递归分块",
        分块大小: int = 1000,
        分块重叠: int = 200,
    ) -> Dict[str, Any]:
        """上传文档到知识库 - 事务+资源管理优化版本"""
        文档UUID = str(uuid.uuid4())

        # 使用轻量级资源管理器
        async with 轻量级资源管理器() as 资源管理器:
            try:
                知识库服务日志器.info(
                    f"🚀 开始处理文档上传: {文件名} -> 知识库 {知识id}"
                )

                # 1. 验证文件存在性和可读性
                if not os.path.exists(文件路径):
                    return {"success": False, "error": f"文件不存在: {文件路径}"}

                if not os.access(文件路径, os.R_OK):
                    return {"success": False, "error": f"文件不可读: {文件路径}"}

                文件大小 = os.path.getsize(文件路径)
                if 文件大小 == 0:
                    return {"success": False, "error": "文件内容为空"}

                # 注册文件资源（异常时清理）
                资源管理器.注册资源("文件", 文件路径)

                # 2. 检查分块策略与文件大小的适配性
                分块策略检查结果 = await self._检查分块策略适配性(文件大小, 分块策略)
                if not 分块策略检查结果["适配"]:
                    return {
                        "success": False,
                        "error": 分块策略检查结果["错误信息"],
                        "建议": 分块策略检查结果.get("建议", []),
                    }

                # 3. 检查是否使用流式处理
                使用流式处理 = 流式文档处理器实例.是否支持流式处理(文件路径)
                内存建议 = 流式文档处理器实例.获取内存使用建议(文件大小)

                知识库服务日志器.info(
                    f"📊 文件分析: {内存建议['文件类型']}, 预计内存: {内存建议['预计内存使用']}"
                )
                知识库服务日志器.info(f"💡 处理建议: {内存建议['建议']}")

                # 3. 处理文档内容和分块
                if 使用流式处理:
                    知识库服务日志器.info("🌊 使用流式处理模式...")
                    处理结果 = await self._流式处理文档(
                        文件路径, 知识id, 文件名, 分块策略, 分块大小, 分块重叠
                    )
                else:
                    知识库服务日志器.info("📄 使用标准处理模式...")
                    # 初始化文档处理器
                    if not LangChain文档处理器实例.已初始化:
                        await LangChain文档处理器实例.初始化()

                    处理结果 = await LangChain文档处理器实例.处理文档文件(
                        文件路径, 知识id, 文件名, 分块策略, 分块大小, 分块重叠
                    )

                if not 处理结果.get("success"):
                    return {
                        "success": False,
                        "error": f"文档处理失败: {处理结果.get('error', '未知错误')}",
                    }

                # 4. 检查MD5重复
                文件MD5 = 处理结果.get("文件MD5", "")
                重复检查结果 = await self._检查文档重复(知识id, 文件MD5, 文件名)
                if not 重复检查结果["success"]:
                    return 重复检查结果

                # 5. 使用事务保存数据
                知识库服务日志器.info("💾 开始事务处理...")

                处理元数据 = 处理结果.get("元数据", {})
                文档分块列表 = 处理结果.get("文档分块", [])
                分块数量 = len(文档分块列表)

                文档数据 = {
                    "文档uuid": 文档UUID,
                    "langchain_知识库表id": 知识id,
                    "文档路径": 文件路径,
                    "文档名称": 文件名,
                    "文档类型": 处理结果.get("文档类型", ""),
                    "文档大小": 文件大小,
                    "文档内容": 处理结果.get("文档内容", ""),
                    "文档状态": "处理中",
                    "向量分块数量": 分块数量,
                    "向量状态": "待处理",
                    "md5": 文件MD5,  # 保存文件MD5值
                    "元数据": {
                        **(处理元数据 if isinstance(处理元数据, dict) else {}),
                        "分块策略": 分块策略,
                        "分块大小": 分块大小,
                        "分块重叠": 分块重叠,
                        "处理时间": datetime.now().isoformat(),
                    },
                }

                # 使用连接池获取连接并管理事务
                async with self.数据层.数据库连接池.获取连接() as 连接:
                    async with 连接.transaction():
                        # 创建文档记录
                        文档记录id = await self.数据层.创建知识库文档(文档数据)
                        if not 文档记录id:
                            raise Exception("创建文档记录失败")

                        # 注册文档记录资源（异常时清理）
                        资源管理器.注册资源("文档记录", 文档记录id)

                        # 批量创建向量记录占位符
                        向量记录id列表 = await self.数据层.批量创建向量记录占位(
                            文档记录id, 分块数量
                        )
                        if len(向量记录id列表) != 分块数量:
                            raise Exception("创建向量占位符失败")

                        # 注册向量数据资源（异常时清理）
                        资源管理器.注册资源("向量数据", 向量记录id列表)

                        # 事务会自动提交
                        知识库服务日志器.info("✅ 事务提交成功")

                # 5. 事务外进行向量化处理（避免长事务）
                知识库服务日志器.info("🔄 开始向量化处理...")
                向量化状态 = "向量化失败"
                向量化成功数量 = 0

                try:
                    # 向量化功能已整合到知识库服务中
                    知识库服务日志器.info("开始文档向量化处理")

                    # 简化的向量化状态更新
                    await self.数据层.通过ID更新文档状态(
                        文档记录id, "已处理", "向量化功能已整合"
                    )
                    向量化状态 = "已完成"

                except Exception as vec_error:
                    知识库服务日志器.error(f"❌ 向量化处理异常: {str(vec_error)}")
                    向量化状态 = "向量化失败"

                # 6. 返回处理结果
                知识库服务日志器.info(
                    f"✅ 文档上传完成: {文件名} -> 知识库 {知识id}, 向量化状态: {向量化状态}"
                )

                return {
                    "success": True,
                    "文档id": 文档记录id,
                    "文档UUID": 文档UUID,
                    "文档名称": 文件名,
                    "文档类型": 处理结果.get("文档类型", ""),
                    "文档大小": 文件大小,
                    "分块数量": 分块数量,
                    "向量化成功数量": 向量化成功数量,
                    "向量化状态": 向量化状态,
                    "文件路径": 文件路径,
                    "处理配置": {
                        "分块策略": 分块策略,
                        "分块大小": 分块大小,
                        "分块重叠": 分块重叠,
                    },
                    "处理时间": datetime.now().isoformat(),
                    "message": "文档上传成功",
                }

            except Exception as e:
                知识库服务日志器.error(f"❌ 文档上传失败: {str(e)}")
                # 资源管理器会自动清理资源
                return {"success": False, "error": f"文档上传失败: {str(e)}"}

    async def _清理文档向量记录(self, 文档记录id: int) -> bool:
        """清理文档的向量记录"""
        try:
            清理SQL = """
            DELETE FROM langchain_文档向量表
            WHERE langchain_知识库文档表id = $1
            """

            影响行数 = await self.数据层.数据库连接池.执行更新(清理SQL, (文档记录id,))
            知识库服务日志器.info(
                f"清理文档向量记录: 文档id {文档记录id}, 清理 {影响行数} 条记录"
            )
            return True

        except Exception as e:
            知识库服务日志器.error(f"清理文档向量记录失败: {str(e)}")
            return False

    async def _流式处理文档(
        self,
        文件路径: str,
        知识id: int,
        文件名: str,
        分块策略: str,
        分块大小: int,
        分块重叠: int,
    ) -> Dict[str, Any]:
        """流式处理文档 - 内存友好的大文件处理"""
        try:
            # 收集所有分块
            文档分块列表 = []
            分块计数 = 0
            完整文档内容 = ""

            知识库服务日志器.info("🌊 开始流式分块处理...")

            # 流式分块处理
            try:
                for 分块信息 in 流式文档处理器实例.流式分块处理(
                    文件路径, 分块策略, 分块大小, 分块重叠
                ):
                    if isinstance(分块信息, dict) and "分块内容" in 分块信息:
                        文档分块列表.append(分块信息)
                        分块计数 += 1

                        # 拼接完整文档内容
                        分块内容 = 分块信息.get("分块内容", "")
                        if 分块内容.strip():
                            完整文档内容 += 分块内容 + "\n"

                        # 每处理10个分块输出一次进度
                        if 分块计数 % 10 == 0:
                            知识库服务日志器.debug(f"已处理 {分块计数} 个分块")
                    elif isinstance(分块信息, dict) and "success" in 分块信息:
                        # 处理完成，返回结果
                        break
            except StopIteration:
                # 生成器正常结束
                pass

            # 获取文档类型
            文档类型 = Path(文件路径).suffix.lower().lstrip(".")

            # 构建处理结果
            处理结果 = {
                "success": True,
                "文档类型": 文档类型,
                "文档内容": 完整文档内容.strip(),  # 保存完整的文档内容
                "文档分块": 文档分块列表,
                "元数据": {
                    "处理方式": "流式处理",
                    "分块数量": 分块计数,
                    "分块策略": 分块策略,
                    "分块大小": 分块大小,
                    "分块重叠": 分块重叠,
                },
            }

            知识库服务日志器.info(f"✅ 流式处理完成: {分块计数} 个分块")
            return 处理结果

        except Exception as e:
            知识库服务日志器.error(f"❌ 流式处理失败: {str(e)}")
            return {"success": False, "error": f"流式处理失败: {str(e)}"}

    async def 上传文档到知识库(
        self,
        知识id: int,
        文件路径: str,
        文件名: str,
        分块策略: str = "智能递归分块",
        分块大小: int = 1000,
        分块重叠: int = 200,
    ) -> Dict[str, Any]:
        """上传文档到知识库 - 原版本保持兼容性"""
        文档UUID = str(uuid.uuid4())
        文档记录id = None

        try:
            知识库服务日志器.info(f"🚀 开始处理文档上传: {文件名} -> 知识库 {知识id}")

            # 1. 验证文件存在性和可读性
            if not os.path.exists(文件路径):
                return {"success": False, "error": f"文件不存在: {文件路径}"}

            if not os.access(文件路径, os.R_OK):
                return {"success": False, "error": f"文件不可读: {文件路径}"}

            文件大小 = os.path.getsize(文件路径)
            if 文件大小 == 0:
                return {"success": False, "error": "文件内容为空"}

            # 2. 初始化文档处理器
            if not LangChain文档处理器实例.已初始化:
                await LangChain文档处理器实例.初始化()

            # 3. 处理文档内容和分块
            知识库服务日志器.info("📄 开始文档内容处理和分块...")
            处理结果 = await LangChain文档处理器实例.处理文档文件(
                文件路径, 知识id, 文件名, 分块策略, 分块大小, 分块重叠
            )

            if not 处理结果.get("success"):
                return {
                    "success": False,
                    "error": f"文档处理失败: {处理结果.get('error', '未知错误')}",
                }

            # 4. 检查MD5重复
            文件MD5 = 处理结果.get("文件MD5", "")
            重复检查结果 = await self._检查文档重复(知识id, 文件MD5, 文件名)
            if not 重复检查结果["success"]:
                return 重复检查结果

            # 5. 准备文档数据并保存到数据库
            知识库服务日志器.info("💾 保存文档记录到数据库...")

            处理元数据 = 处理结果.get("元数据", {})
            文档分块列表 = 处理结果.get("文档分块", [])
            分块数量 = len(文档分块列表)

            文档数据 = {
                "文档uuid": 文档UUID,  # 使用预生成的UUID
                "langchain_知识库表id": 知识id,
                "文档路径": 文件路径,  # 保存文件路径
                "文档名称": 文件名,
                "文档类型": 处理结果.get("文档类型", ""),
                "文档大小": 文件大小,
                "文档内容": 处理结果.get("文档内容", ""),
                "文档状态": "处理中",  # 初始状态为处理中
                "向量分块数量": 分块数量,  # 直接设置表字段
                "向量状态": "待处理",  # 直接设置表字段
                "md5": 文件MD5,  # 保存文件MD5值
                "元数据": {
                    **(处理元数据 if isinstance(处理元数据, dict) else {}),
                    "分块策略": 分块策略,
                    "分块大小": 分块大小,
                    "分块重叠": 分块重叠,
                    "处理时间": datetime.now().isoformat(),
                    # 移除冗余的分块数量和向量化状态字段
                },
            }

            知识库服务日志器.debug(f"创建文档记录: {文档数据}")
            文档记录id = await self.数据层.创建知识库文档(文档数据)

            if not 文档记录id:
                return {"success": False, "error": "保存文档记录到数据库失败"}

            # 5. 进行向量化处理
            向量化状态 = "向量化失败"
            向量化成功数量 = 0

            try:
                知识库服务日志器.info("🔮 开始向量化处理...")

                # 向量化功能已整合到知识库服务中
                知识库服务日志器.info("执行简化的向量化处理")

                # 简化的向量化状态更新
                await self.数据层.通过ID更新文档状态(
                    文档记录id, "已处理", "向量化功能已整合"
                )
                向量化状态 = "已完成"

            except Exception as vec_error:
                知识库服务日志器.error(f"❌ 向量化处理异常: {str(vec_error)}")
                向量化状态 = "向量化失败"
                if 文档记录id:
                    await self.数据层.通过ID更新文档状态(
                        文档记录id, "失败", str(vec_error)
                    )
                    await self.数据层.更新文档向量状态(文档记录id, "失败")

            # 6. 返回处理结果
            知识库服务日志器.info(
                f"✅ 文档上传完成: {文件名} -> 知识库 {知识id}, 向量化状态: {向量化状态}"
            )

            return {
                "success": True,
                "文档id": 文档记录id,
                "文档UUID": 文档UUID,
                "文档名称": 文件名,
                "文档类型": 处理结果.get("文档类型", ""),
                "文档大小": 文件大小,
                "分块数量": 分块数量,
                "向量化成功数量": 向量化成功数量,
                "向量化状态": 向量化状态,
                "文件路径": 文件路径,
                "处理配置": {
                    "分块策略": 分块策略,
                    "分块大小": 分块大小,
                    "分块重叠": 分块重叠,
                },
                "处理时间": datetime.now().isoformat(),
                "message": "文档上传成功",
            }

        except Exception as e:
            知识库服务日志器.error(f"❌ 文档上传处理失败: {str(e)}")

            # 清理已创建的文档记录
            if 文档记录id:
                try:
                    await self.数据层.删除知识库文档(文档记录id)
                    知识库服务日志器.info(f"已清理失败的文档记录: {文档记录id}")
                except Exception as cleanup_error:
                    知识库服务日志器.warning(f"清理文档记录失败: {cleanup_error}")

            return {"success": False, "error": f"文档上传处理失败: {str(e)}"}

    async def _向量化文档分块(
        self, 文档记录id: int, 文档分块: List[Dict]
    ) -> Dict[str, Any]:
        """向量化文档分块并保存到PostgreSQL"""
        try:
            # 直接使用分块数据进行向量化
            成功数量 = 0
            失败数量 = 0

            知识库服务日志器.info(f"🔄 开始向量化 {len(文档分块)} 个分块...")

            for i, 分块信息 in enumerate(文档分块):
                try:
                    分块内容 = 分块信息.get("分块内容", "")
                    if not 分块内容.strip():
                        continue

                    # 简化的向量化处理
                    知识库服务日志器.debug(f"处理分块 {i}: {分块内容[:50]}...")
                    成功数量 += 1

                except Exception as e:
                    失败数量 += 1
                    知识库服务日志器.error(f"分块 {i} 向量化异常: {str(e)}")

            # 更新文档状态
            if 成功数量 > 0:
                if 失败数量 == 0:
                    await self._更新文档状态(文档记录id, "已完成", "已完成")
                else:
                    await self._更新文档状态(文档记录id, "部分完成", "部分完成")
            else:
                await self._更新文档状态(文档记录id, "失败", "失败")

            知识库服务日志器.info(f"✅ 向量化完成: 成功 {成功数量}, 失败 {失败数量}")

            return {
                "success": 成功数量 > 0,
                "向量化成功数量": 成功数量,
                "向量化失败数量": 失败数量,
            }

        except Exception as e:
            知识库服务日志器.error(f"向量化文档分块失败: {str(e)}")
            return {"success": False, "error": f"向量化失败: {str(e)}"}

    async def _更新文档状态(
        self, 文档记录id: int, 文档状态: str, 向量状态: str
    ) -> bool:
        """更新文档状态 - 包含最后向量化时间"""
        try:
            # 如果向量状态是完成状态，则更新最后向量化时间
            if 向量状态 in ["已完成", "部分完成"]:
                更新SQL = """
                UPDATE langchain_知识库文档表
                SET 文档状态 = $1, 向量状态 = $2, 最后向量化时间 = NOW(), 更新时间 = NOW()
                WHERE id = $3
                """
            else:
                更新SQL = """
                UPDATE langchain_知识库文档表
                SET 文档状态 = $1, 向量状态 = $2, 更新时间 = NOW()
                WHERE id = $3
                """

            async with self.数据层.数据库连接池.获取连接() as 连接:
                await 连接.execute(
                    更新SQL, str(文档状态), str(向量状态), int(文档记录id)
                )
            return True
        except Exception as e:
            知识库服务日志器.error(f"更新文档状态失败: {str(e)}")
            return False

    async def 通过UUID删除知识库文档(self, 文档UUID: str) -> Dict[str, Any]:
        """通过UUID删除知识库文档"""
        try:
            删除成功 = await self.数据层.通过UUID删除知识库文档(文档UUID)

            if 删除成功:
                return {"success": True, "message": "文档删除成功"}
            else:
                return {"success": False, "error": "文档删除失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID删除知识库文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID删除知识库文档失败: {str(e)}"}

    async def 通过ID删除知识库文档(self, 文档记录id: int) -> Dict[str, Any]:
        """通过数字ID删除知识库文档"""
        try:
            删除成功 = await self.数据层.通过ID删除知识库文档(文档记录id)

            if 删除成功:
                return {"success": True, "message": "文档删除成功"}
            else:
                return {"success": False, "error": "文档删除失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过ID删除知识库文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID删除知识库文档失败: {str(e)}"}

    async def 搜索文档(
        self, 知识id: int, 搜索关键词: str, 页码: int = 1, 每页数量: int = 10
    ) -> Dict[str, Any]:
        """搜索文档"""
        try:
            # 简化搜索实现 - 返回空结果
            # TODO: 实现具体的文档搜索逻辑，使用搜索关键词: {搜索关键词}
            文档列表, 总数量 = [], 0

            return {
                "success": True,
                "文档列表": 文档列表,
                "总数量": 总数量,
                "当前页码": 页码,
                "每页数量": 每页数量,
            }

        except Exception as e:
            知识库服务日志器.error(f"搜索文档失败: {str(e)}")
            return {"success": False, "error": f"搜索文档失败: {str(e)}"}

    # ==================== 向量化相关方法 ====================

    async def 向量化知识库(
        self,
        知识id: int,
        向量存储类型: str = "postgresql",
        云服务配置: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """向量化知识库 - 使用内置向量化功能"""
        try:

            知识库服务日志器.info(f"开始向量化知识库: {知识id}")

            # 这里可以添加具体的向量化逻辑
            # 目前返回成功状态，实际向量化在文档上传时完成
            return {
                "success": True,
                "message": "知识库向量化功能已整合到文档处理流程中",
                "向量存储类型": "postgresql",
            }

        except Exception as e:
            知识库服务日志器.error(f"向量化知识库失败: {str(e)}")
            return {"success": False, "error": f"向量化知识库失败: {str(e)}"}

    async def 获取向量化状态(self, 知识id: int) -> Dict[str, Any]:
        """获取知识库向量化状态"""
        try:
            # 调用数据层获取向量化状态
            向量化状态 = await self.数据层.获取知识库向量化状态(知识id)

            return {"success": True, "向量化状态": 向量化状态}

        except Exception as e:
            知识库服务日志器.error(f"获取向量化状态失败: {str(e)}")
            return {"success": False, "error": f"获取向量化状态失败: {str(e)}"}

    async def 手动向量化文档(self, 文档记录id: int) -> Dict[str, Any]:
        """手动向量化单个文档"""
        try:
            # 使用文档处理器进行向量化
            if not LangChain文档处理器实例.已初始化:
                await LangChain文档处理器实例.初始化()

            # 执行文档向量化
            向量化结果 = await LangChain文档处理器实例.手动向量化文档(文档记录id)

            if 向量化结果.get("success"):
                知识库服务日志器.info(f"手动向量化文档成功: {文档记录id}")
                return {
                    "success": True,
                    "message": "文档向量化成功",
                    "向量化统计": 向量化结果.get("统计信息", {}),
                }
            else:
                return {
                    "success": False,
                    "error": 向量化结果.get("error", "文档向量化失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"手动向量化文档失败: {str(e)}")
            return {"success": False, "error": f"手动向量化文档失败: {str(e)}"}

    # ==================== 向量检索相关方法 ====================

    async def 智能多层检索(
        self,
        知识id: int,
        查询文本: str,
        最大数量: int = 10,
        相似度阈值: float = 0.7,
        最小结果数量: int = 3,
        启用自适应阈值: bool = True,
        查询优化配置: Optional[Dict[str, Any]] = None,
        嵌入模型id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """智能多层检索 - 解决高阈值导致结果不足的问题"""
        try:
            知识库服务日志器.info(
                f"🔍 智能多层检索: 知识id={知识id}, 阈值={相似度阈值}, 最小结果数={最小结果数量}"
            )

            # 定义检索层级配置 (简化为3层)
            检索层级 = [
                ("精确检索", 相似度阈值, 最大数量),
                ("扩展检索", 相似度阈值 * 0.7, min(最大数量 * 2, 15)),
                ("兜底检索", 0.3, min(最大数量 * 3, 20)),
            ]

            # 逐层尝试检索
            for i, (层级名称, 当前阈值, 当前数量) in enumerate(检索层级):
                知识库服务日志器.debug(
                    f"📊 第{i + 1}层: {层级名称} (阈值={当前阈值:.3f}, 数量={当前数量})"
                )

                当前结果 = await self.PostgreSQL向量检索(
                    知识id=知识id,
                    查询文本=查询文本,
                    最大数量=当前数量,
                    相似度阈值=当前阈值,
                    查询优化配置=查询优化配置,
                    嵌入模型id=嵌入模型id,
                )

                if 当前结果.get("success"):
                    结果数量 = len(当前结果.get("检索结果", []))

                    # 检查是否满足要求 (最后一层或数量充足)
                    if 结果数量 >= 最小结果数量 or i == len(检索层级) - 1:
                        # 增强结果信息
                        当前结果.update(
                            {
                                "使用多层检索": True,
                                "成功层级": 层级名称,
                                "最终阈值": 当前阈值,
                                "尝试层数": i + 1,
                            }
                        )

                        知识库服务日志器.info(
                            f"✅ {层级名称}成功: {结果数量}个结果 (阈值={当前阈值:.3f})"
                        )
                        return 当前结果
                    else:
                        知识库服务日志器.debug(
                            f"⚠️ {层级名称}结果不足: {结果数量} < {最小结果数量}"
                        )

            # 所有层级都失败
            知识库服务日志器.warning("❌ 所有检索层级都失败")
            return {
                "success": False,
                "error": "多层检索全部失败",
                "使用多层检索": True,
            }

        except Exception as e:
            知识库服务日志器.error(f"智能多层检索异常: {str(e)}")
            return {
                "success": False,
                "error": f"智能多层检索异常: {str(e)}",
                "使用多层检索": True,
            }

    def _简单质量评估(self, 检索结果: List[Dict], 相似度阈值: float) -> float:
        """简化的质量评估 - 基于平均相似度"""
        if not 检索结果:
            return 0.0

        相似度分数列表 = [结果.get("相似度分数", 0) for 结果 in 检索结果]
        平均相似度 = sum(相似度分数列表) / len(相似度分数列表)

        # 简单的质量分数：平均相似度相对于阈值的比例
        质量分数 = min(1.0, 平均相似度 / max(相似度阈值, 0.1))
        return 质量分数

    async def PostgreSQL关键词检索(
        self,
        知识id: int,
        查询文本: str,
        最大数量: int = 10,
        语言配置: str = "simple",
    ) -> Dict[str, Any]:
        """PostgreSQL关键词检索 - 基于全文搜索的关键词匹配"""
        try:
            知识库服务日志器.info(
                f"PostgreSQL关键词检索: 知识id={知识id}, 查询='{查询文本}', "
                f"最大数量={最大数量}, 语言配置={语言配置}"
            )

            # 获取知识库详情
            知识库详情 = await self.数据层.获取知识库详情(知识id)
            if not 知识库详情:
                return {"success": False, "error": f"知识库 {知识id} 不存在"}

            # 检查知识库存储类型
            存储类型 = 知识库详情.get("存储类型", "postgresql")
            if 存储类型 != "postgresql":
                return {
                    "success": False,
                    "error": f"知识库 {知识id} 存储类型不支持关键词检索: {存储类型}",
                }

            # 检查PostgreSQL文档数据
            检查SQL = """
            SELECT COUNT(*) as 文档数量
            FROM langchain_知识库文档表 d
            WHERE d.langchain_知识库表id = $1
            AND d.向量状态 IN ('已完成', '基本完成')
            """

            async with self.数据层.数据库连接池.获取连接() as 连接:
                结果 = await 连接.fetchrow(检查SQL, int(知识id))

            文档数量 = 结果["文档数量"] if 结果 else 0
            知识库服务日志器.info(f"📊 知识库 {知识id} 包含 {文档数量} 个文档")

            if 文档数量 == 0:
                return {
                    "success": False,
                    "error": f"知识库 {知识id} 没有可检索的文档数据",
                }

            # 处理查询文本，转换为tsquery格式
            处理后查询 = self._处理关键词查询(查询文本)

            # 提取原始关键词用于LIKE搜索
            import re

            原始关键词 = re.sub(r"[^\w\s\u4e00-\u9fff]", " ", 查询文本)
            关键词列表 = [
                word.strip()
                for word in 原始关键词.split()
                if word.strip() and len(word.strip()) >= 2
            ]

            知识库服务日志器.debug(
                f"原始查询: '{查询文本}' -> 处理后查询: '{处理后查询}', 关键词: {关键词列表}"
            )

            # 构建混合搜索SQL：全文搜索 + LIKE模糊匹配
            搜索SQL = """
            WITH 全文搜索 AS (
                SELECT
                    d.id as 文档记录id,
                    d.文档uuid,
                    d.文档名称,
                    d.文档类型,
                    v.分块内容,
                    v.分块序号,
                    v.元数据,
                    ts_rank(
                        to_tsvector($3, COALESCE(v.分块内容, '')),
                        to_tsquery($3, $4)
                    ) as 相关性分数,
                    '全文搜索' as 匹配方式
                FROM langchain_知识库文档表 d
                JOIN langchain_文档向量表 v ON v.langchain_知识库文档表id = d.id
                WHERE d.langchain_知识库表id = $1
                AND d.向量状态 IN ('已完成', '基本完成')
                AND v.分块内容 IS NOT NULL
                AND to_tsvector($3, COALESCE(v.分块内容, '')) @@ to_tsquery($3, $4)
            ),
            模糊搜索 AS (
                SELECT
                    d.id as 文档记录id,
                    d.文档uuid,
                    d.文档名称,
                    d.文档类型,
                    v.分块内容,
                    v.分块序号,
                    v.元数据,
                    0.3 as 相关性分数,  -- 模糊匹配给较低分数
                    'LIKE匹配' as 匹配方式
                FROM langchain_知识库文档表 d
                JOIN langchain_文档向量表 v ON v.langchain_知识库文档表id = d.id
                WHERE d.langchain_知识库表id = $1
                AND d.向量状态 IN ('已完成', '基本完成')
                AND v.分块内容 IS NOT NULL
                AND v.分块内容 LIKE '%' || $5 || '%'
                AND d.id NOT IN (SELECT 文档记录id FROM 全文搜索)  -- 避免重复
            )
            SELECT * FROM 全文搜索
            UNION ALL
            SELECT * FROM 模糊搜索
            ORDER BY 相关性分数 DESC
            LIMIT $2
            """

            async with self.数据层.数据库连接池.获取连接() as 连接:
                # 使用第一个关键词进行LIKE搜索
                模糊搜索关键词 = 关键词列表[0] if 关键词列表 else 查询文本
                检索记录 = await 连接.fetch(
                    搜索SQL, int(知识id), 最大数量, 语言配置, 处理后查询, 模糊搜索关键词
                )

            # 处理检索结果
            结果列表 = []
            文档记录id集合 = set()

            for 记录 in 检索记录:
                文档记录id = 记录["文档记录id"]
                if 文档记录id in 文档记录id集合:
                    continue
                文档记录id集合.add(文档记录id)

                # 构建结果项
                结果项 = {
                    "文档记录id": 文档记录id,
                    "文档UUID": str(记录["文档uuid"]) if 记录["文档uuid"] else "",
                    "文档名称": 记录["文档名称"] or "",
                    "文档类型": 记录["文档类型"] or "",
                    "分块内容": 记录["分块内容"] or "",
                    "分块序号": 记录["分块序号"] or 0,
                    "分块元数据": 记录["元数据"] or {},
                    "相关性分数": float(记录["相关性分数"])
                    if 记录["相关性分数"]
                    else 0.0,
                    "检索方式": "关键词匹配",
                }

                结果列表.append(结果项)

            # 添加详细的调试信息
            if 结果列表:
                最高分数 = max(结果["相关性分数"] for 结果 in 结果列表)
                最低分数 = min(结果["相关性分数"] for 结果 in 结果列表)
                知识库服务日志器.info(
                    f"PostgreSQL关键词检索完成: 知识id {知识id}, 查询: '{查询文本}', "
                    f"返回: {len(结果列表)} 个结果, "
                    f"相关性分数范围: [{最低分数:.4f}, {最高分数:.4f}]"
                )
            else:
                知识库服务日志器.warning(
                    f"PostgreSQL关键词检索无结果: 知识id {知识id}, 查询: '{查询文本}'"
                )

            return {
                "success": True,
                "查询文本": 查询文本,
                "知识id": 知识id,
                "结果数量": len(结果列表),
                "检索结果": 结果列表,
                "检索统计": {
                    "语言配置": 语言配置,
                    "最终结果数量": len(结果列表),
                    "检索方式": "关键词匹配",
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"PostgreSQL关键词检索失败: {str(e)}")
            return {"success": False, "error": f"关键词检索失败: {str(e)}"}

    def _处理关键词查询(self, 查询文本: str) -> str:
        """处理关键词查询文本，转换为PostgreSQL tsquery格式"""
        try:
            import re

            # 移除特殊字符，保留字母、数字、空格和中文
            清理后文本 = re.sub(r"[^\w\s\u4e00-\u9fff]", " ", 查询文本)
            # 分割单词并过滤空字符串
            单词列表 = [word.strip() for word in 清理后文本.split() if word.strip()]

            if not 单词列表:
                return 查询文本

            # 定义停用词和疑问词
            停用词 = {
                "的",
                "了",
                "在",
                "是",
                "有",
                "和",
                "与",
                "或",
                "但",
                "而",
                "也",
                "都",
                "很",
                "更",
                "最",
                "点",
                "些",
                "这",
                "那",
                "什么",
                "怎么",
                "为什么",
                "如何",
            }
            疑问词 = {"什么", "怎么", "为什么", "如何", "哪里", "谁", "何时", "多少"}

            # 分类单词：重要词汇 vs 一般词汇
            重要词汇 = []
            一般词汇 = []

            for 词 in 单词列表:
                if len(词) >= 2 and 词 not in 停用词:  # 长度>=2且非停用词的为重要词汇
                    重要词汇.append(词)
                elif 词 not in 疑问词:  # 非疑问词的一般词汇
                    一般词汇.append(词)

            # 构建查询策略
            if 重要词汇:
                if len(重要词汇) == 1:
                    # 只有一个重要词汇，直接使用
                    return 重要词汇[0]
                else:
                    # 多个重要词汇，使用OR逻辑连接，提高召回率
                    return " | ".join(重要词汇)
            elif 一般词汇:
                # 只有一般词汇，使用OR逻辑
                return " | ".join(一般词汇)
            else:
                # 都是停用词，使用原查询
                return 查询文本

        except Exception as e:
            知识库服务日志器.warning(f"处理关键词查询失败: {str(e)}, 使用原查询")
            return 查询文本

    async def PostgreSQL向量检索(
        self,
        知识id: int,
        查询文本: str,
        最大数量: int = 10,
        相似度阈值: float = 0.5,
        查询优化配置: Optional[Dict[str, Any]] = None,
        嵌入模型id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """PostgreSQL向量检索"""
        try:
            知识库服务日志器.info(
                f"PostgreSQL向量检索: 知识id={知识id}, 查询='{查询文本}', "
                f"最大数量={最大数量}, 相似度阈值={相似度阈值}"
            )

            # 获取知识库详情
            知识库详情 = await self.数据层.获取知识库详情(知识id)
            if not 知识库详情:
                return {"success": False, "error": f"知识库 {知识id} 不存在"}

            # 检查知识库存储类型
            存储类型 = 知识库详情.get("存储类型", "postgresql")
            if 存储类型 != "postgresql":
                return {
                    "success": False,
                    "error": f"知识库 {知识id} 存储类型不支持: {存储类型}",
                }

            # 如果没有传递查询优化配置，使用知识库的默认配置
            if 查询优化配置 is None:
                默认启用查询优化 = 知识库详情.get("默认启用查询优化", 0)
                if 默认启用查询优化:
                    查询优化配置 = {
                        "启用": True,
                        "优化策略": 知识库详情.get("默认查询优化策略", "rewrite"),
                        "优化模型id": 知识库详情.get("默认查询优化模型id"),
                        "提示词模板": 知识库详情.get("默认查询优化提示词", ""),
                    }
                    知识库服务日志器.info(
                        f"使用知识库 {知识id} 的默认查询优化配置: {查询优化配置}"
                    )
                else:
                    查询优化配置 = {"启用": False}
                    知识库服务日志器.debug(f"知识库 {知识id} 未启用查询优化")

            # RAG引擎功能已整合到知识库服务中，无需外部初始化

            # 检查PostgreSQL向量数据

            检查SQL = """
            SELECT COUNT(*) as 向量数量
            FROM langchain_文档向量表 v
            JOIN langchain_知识库文档表 d ON v.langchain_知识库文档表id = d.id
            WHERE d.langchain_知识库表id = $1
            AND d.向量状态 IN ('已完成', '基本完成')
            AND v.向量数据 IS NOT NULL
            """

            async with self.数据层.数据库连接池.获取连接() as 连接:
                结果 = await 连接.fetchrow(检查SQL, int(知识id))

            向量数量 = 结果["向量数量"] if 结果 else 0
            知识库服务日志器.info(f"📊 知识库 {知识id} 包含 {向量数量} 个向量")

            if 向量数量 == 0:
                return {
                    "success": False,
                    "error": f"知识库 {知识id} 没有可检索的向量数据",
                }

            # 使用内置的PostgreSQL向量检索
            检索结果列表 = await self._执行PostgreSQL向量检索(
                查询文本, 知识id, 最大数量, 相似度阈值, 嵌入模型id
            )

            # 处理PostgreSQL检索结果
            结果列表 = []
            文档记录id集合 = set()

            for document in 检索结果列表:
                try:
                    # 检查是否是字典格式（来自_执行PostgreSQL向量检索）
                    if isinstance(document, dict):
                        # 直接使用字典格式的结果
                        文档记录id = document.get("文档记录id", 0)
                        if 文档记录id:
                            文档记录id集合.add(文档记录id)

                        相似度分数 = document.get("相似度分数", 0.0)
                        文档名称 = document.get("文档名称", "")
                        文档uuid = document.get("文档uuid", "")
                        分块序号 = document.get("分块序号", 0)
                        分块内容 = document.get("分块内容", "")
                        元数据 = document.get("元数据", {})  # 确保元数据变量存在
                    else:
                        # 处理LangChain Document对象格式
                        元数据 = getattr(document, "metadata", {})
                        文档记录id = 元数据.get("document_record_id", 0)
                        if 文档记录id:
                            文档记录id集合.add(文档记录id)

                        相似度分数 = 元数据.get("相似度分数", 0.0)
                        文档名称 = 元数据.get("文档名称", "")
                        文档uuid = 元数据.get("文档uuid", "")
                        分块序号 = 元数据.get("分块序号", 0)
                        分块内容 = getattr(document, "page_content", "")

                    # 确保相似度在合理范围内
                    相似度分数 = max(0.0, min(1.0, 相似度分数))

                    # 添加详细的调试日志
                    知识库服务日志器.debug(
                        f"处理PostgreSQL检索结果: 相似度={相似度分数:.6f}, 阈值={相似度阈值}, "
                        f"文档={文档名称}, 内容预览='{分块内容[:50]}...'"
                    )

                    # 构建标准化的结果项，确保字段名与智能体服务期望的一致
                    结果项 = {
                        "分块内容": 分块内容,
                        "相似度分数": round(相似度分数, 4),
                        "文档名称": 文档名称,
                        "文档uuid": 文档uuid,
                        "分块序号": 分块序号,
                        "知识id": 知识id,
                        "文档记录id": 文档记录id,
                        "分块大小": len(分块内容),
                        # 简化元数据处理
                        "元数据": self._清理元数据(元数据),
                    }
                    结果列表.append(结果项)
                except Exception as e:
                    知识库服务日志器.warning(f"处理检索结果失败: {str(e)}")
                    continue

            # 确保所有必要字段都存在
            for 结果项 in 结果列表:
                if not 结果项.get("文档名称"):
                    结果项["文档名称"] = f"文档_{结果项.get('文档记录id', 'unknown')}"

            # 添加详细的调试信息
            if 结果列表:
                最高相似度 = max(结果["相似度分数"] for 结果 in 结果列表)
                最低相似度 = min(结果["相似度分数"] for 结果 in 结果列表)
                知识库服务日志器.info(
                    f"PostgreSQL向量检索完成: 知识id {知识id}, 查询: '{查询文本}', "
                    f"返回: {len(结果列表)} 个结果, "
                    f"相似度范围: [{最低相似度:.4f}, {最高相似度:.4f}], 阈值: {相似度阈值}"
                )
            else:
                知识库服务日志器.warning(
                    f"PostgreSQL向量检索无结果: 知识id {知识id}, 查询: '{查询文本}', "
                    f"阈值: {相似度阈值}"
                )

            # 查询优化功能已整合到知识库服务中
            查询优化信息 = None  # 简化处理

            # 转换查询优化信息为前端兼容格式
            转换后的查询优化信息 = None
            if 查询优化信息:
                # 检查是否为新的三次优化格式
                if "第一次优化" in 查询优化信息:
                    # 新的三次优化格式，直接使用
                    优化策略 = 查询优化信息.get("优化策略", "rewrite")
                    策略名称映射 = {
                        "rewrite": "查询重写",
                        "expand": "查询扩展",
                        "multi_query": "查询分解",
                    }
                    策略名称 = 策略名称映射.get(优化策略, "查询重写")

                    第一次优化 = 查询优化信息.get("第一次优化", {})
                    第二次优化 = 查询优化信息.get("第二次优化", {})
                    第三次优化 = 查询优化信息.get("第三次优化", {})

                    转换后的查询优化信息 = {
                        "第一次优化": {
                            "策略": "原始查询",
                            "查询": 第一次优化.get("查询", 查询文本),
                            "结果数量": 第一次优化.get("结果数量", 0),
                            "最高相似度": 第一次优化.get("最高相似度", 0.0),
                            "检索结果": [],
                        },
                        "第二次优化": {
                            "策略": f"{策略名称}变体1",
                            "查询": 第二次优化.get("查询", ""),
                            "结果数量": 第二次优化.get("结果数量", 0),
                            "最高相似度": 第二次优化.get("最高相似度", 0.0),
                            "检索结果": [],
                        },
                        "第三次优化": {
                            "策略": f"{策略名称}变体2",
                            "查询": 第三次优化.get("查询", ""),
                            "结果数量": 第三次优化.get("结果数量", 0),
                            "最高相似度": 第三次优化.get("最高相似度", 0.0),
                            "检索结果": [],
                        },
                        "最终选择": 查询优化信息.get("最终选择", "原始查询"),
                        "最终查询": 查询优化信息.get("最终查询", 查询文本),
                        "优化效果类型": 查询优化信息.get("优化效果类型", "info"),
                        "优化效果描述": 查询优化信息.get(
                            "优化效果描述", f"使用{策略名称}策略进行三次优化"
                        ),
                        "性能统计": 查询优化信息.get("性能统计", {}),
                        "优化对比": {
                            "最高相似度": max(
                                第一次优化.get("最高相似度", 0.0),
                                第二次优化.get("最高相似度", 0.0),
                                第三次优化.get("最高相似度", 0.0),
                            ),
                            "优化策略": 策略名称,
                            "三次结果": [
                                第一次优化.get("最高相似度", 0.0),
                                第二次优化.get("最高相似度", 0.0),
                                第三次优化.get("最高相似度", 0.0),
                            ],
                        },
                    }
                else:
                    # 兼容旧格式（如果还有的话）
                    转换后的查询优化信息 = 查询优化信息

            else:
                # 保持原有格式兼容性
                转换后的查询优化信息 = 查询优化信息

            return {
                "success": True,
                "查询文本": 查询文本,
                "知识id": 知识id,
                "结果数量": len(结果列表),
                "检索结果": 结果列表,
                "查询优化信息": 转换后的查询优化信息,
                "检索统计": {
                    "相似度阈值": 相似度阈值,
                    "最终结果数量": len(结果列表),
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"PostgreSQL向量检索失败: {str(e)}")
            return {"success": False, "error": f"向量检索失败: {str(e)}"}

    async def 混合检索(
        self,
        知识id: int,
        查询文本: str,
        最大数量: int = 10,
        相似度阈值: float = 0.5,
        向量权重: float = 0.7,
        关键词权重: float = 0.3,
    ) -> Dict[str, Any]:
        """混合检索：BM25关键词检索 + 向量相似度检索的加权融合"""
        try:
            知识库服务日志器.info(
                f"混合检索开始: 知识id={知识id}, 查询='{查询文本}', "
                f"向量权重={向量权重}, 关键词权重={关键词权重}"
            )

            # 1. 并行执行向量检索和关键词检索
            import asyncio

            # 增加检索数量以获得更多候选结果用于融合
            扩展数量 = min(最大数量 * 3, 50)

            向量检索任务 = self.PostgreSQL向量检索(
                知识id=知识id,
                查询文本=查询文本,
                最大数量=扩展数量,
                相似度阈值=相似度阈值,
                查询优化配置=None,  # 混合检索不使用查询优化
            )

            关键词检索任务 = self.PostgreSQL关键词检索(
                知识id=知识id,
                查询文本=查询文本,
                最大数量=扩展数量,
                语言配置="simple",
            )

            # 并行执行两种检索
            向量检索结果, 关键词检索结果 = await asyncio.gather(
                向量检索任务, 关键词检索任务, return_exceptions=True
            )

            # 检查检索结果并标准化
            if isinstance(向量检索结果, Exception):
                知识库服务日志器.error(f"向量检索失败: {str(向量检索结果)}")
                向量检索结果 = {"success": False, "检索结果": []}
            elif not isinstance(向量检索结果, dict):
                向量检索结果 = {"success": False, "检索结果": []}

            if isinstance(关键词检索结果, Exception):
                知识库服务日志器.error(f"关键词检索失败: {str(关键词检索结果)}")
                关键词检索结果 = {"success": False, "检索结果": []}
            elif not isinstance(关键词检索结果, dict):
                关键词检索结果 = {"success": False, "检索结果": []}

            # 安全地提取结果列表
            向量结果列表 = 向量检索结果.get("检索结果", []) if 向量检索结果.get("success") else []
            关键词结果列表 = 关键词检索结果.get("检索结果", []) if 关键词检索结果.get("success") else []

            知识库服务日志器.info(
                f"检索结果统计: 向量检索={len(向量结果列表)}个, 关键词检索={len(关键词结果列表)}个"
            )

            # 2. 构建文档ID到结果的映射
            向量结果映射 = {}
            关键词结果映射 = {}

            # 处理向量检索结果
            for 结果 in 向量结果列表:
                文档记录id = 结果.get("文档记录id")
                if 文档记录id:
                    向量结果映射[文档记录id] = 结果

            # 处理关键词检索结果
            for 结果 in 关键词结果列表:
                文档记录id = 结果.get("文档记录id")
                if 文档记录id:
                    关键词结果映射[文档记录id] = 结果

            # 3. 融合结果：计算加权分数
            融合结果映射 = {}
            所有文档ID = set(向量结果映射.keys()) | set(关键词结果映射.keys())

            for 文档记录id in 所有文档ID:
                向量结果 = 向量结果映射.get(文档记录id)
                关键词结果 = 关键词结果映射.get(文档记录id)

                # 归一化分数到[0,1]范围
                向量分数 = 0.0
                关键词分数 = 0.0

                if 向量结果:
                    # 向量相似度分数已经在[0,1]范围内
                    向量分数 = 向量结果.get("相似度分数", 0.0)

                if 关键词结果:
                    # BM25相关性分数需要归一化，使用sigmoid函数将分数映射到[0,1]
                    import math

                    原始关键词分数 = 关键词结果.get("相关性分数", 0.0)
                    关键词分数 = 1 / (1 + math.exp(-原始关键词分数))

                # 计算加权融合分数
                融合分数 = 向量权重 * 向量分数 + 关键词权重 * 关键词分数

                # 选择更完整的结果作为基础（优先选择向量结果，因为包含更多元数据）
                基础结果 = 向量结果 if 向量结果 else 关键词结果

                if 基础结果:
                    融合结果 = 基础结果.copy()
                    融合结果.update(
                        {
                            "混合分数": round(融合分数, 4),
                            "向量分数": round(向量分数, 4),
                            "关键词分数": round(关键词分数, 4),
                            "检索方式": "混合检索(BM25+向量)",
                            "融合权重": f"向量:{向量权重}, 关键词:{关键词权重}",
                        }
                    )

                    # 如果同时存在两种结果，添加详细信息
                    if 向量结果 and 关键词结果:
                        融合结果["匹配类型"] = "向量+关键词"
                    elif 向量结果:
                        融合结果["匹配类型"] = "仅向量"
                    else:
                        融合结果["匹配类型"] = "仅关键词"

                    融合结果映射[文档记录id] = 融合结果

            # 4. 按融合分数排序并返回指定数量的结果
            融合结果列表 = list(融合结果映射.values())
            融合结果列表.sort(key=lambda x: x.get("混合分数", 0), reverse=True)

            # 截取到指定数量
            最终结果列表 = 融合结果列表[:最大数量]

            # 5. 统计信息
            if 最终结果列表:
                最高分数 = max(结果["混合分数"] for 结果 in 最终结果列表)
                最低分数 = min(结果["混合分数"] for 结果 in 最终结果列表)
                向量匹配数 = len([r for r in 最终结果列表 if r.get("向量分数", 0) > 0])
                关键词匹配数 = len(
                    [r for r in 最终结果列表 if r.get("关键词分数", 0) > 0]
                )

                知识库服务日志器.info(
                    f"混合检索完成: 返回{len(最终结果列表)}个结果, "
                    f"混合分数范围[{最低分数:.4f}, {最高分数:.4f}], "
                    f"向量匹配:{向量匹配数}个, 关键词匹配:{关键词匹配数}个"
                )
            else:
                知识库服务日志器.warning(
                    f"混合检索无结果: 知识id {知识id}, 查询: '{查询文本}'"
                )

            return {
                "success": True,
                "查询文本": 查询文本,
                "知识id": 知识id,
                "结果数量": len(最终结果列表),
                "检索结果": 最终结果列表,
                "检索统计": {
                    "向量检索结果数": len(向量结果列表),
                    "关键词检索结果数": len(关键词结果列表),
                    "融合结果数": len(融合结果列表),
                    "最终返回数": len(最终结果列表),
                    "向量权重": 向量权重,
                    "关键词权重": 关键词权重,
                    "检索方式": "混合检索(BM25+向量)",
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"混合检索失败: {str(e)}")
            return {"success": False, "error": f"混合检索失败: {str(e)}"}

    def _获取当前向量存储类型(self) -> str:
        """获取当前向量存储类型"""
        return "PostgreSQL"

    def _智能选择检索策略(
        self, 查询文本: str, 知识库详情: Optional[Dict[str, Any]] = None
    ) -> str:
        """智能选择检索策略：根据查询类型自动选择最适合的检索策略"""
        try:
            import re

            # 如果没有嵌入模型，只能使用关键词检索
            if not 知识库详情 or not (
                知识库详情.get("嵌入模型名称") or 知识库详情.get("嵌入模型")
            ):
                知识库服务日志器.info("无嵌入模型，选择关键词检索策略")
                return "keyword"

            # 分析查询文本特征
            查询长度 = len(查询文本.strip())

            # 1. 检测精确匹配模式
            精确匹配模式 = [
                r'"[^"]*"',  # 双引号包围的精确短语
                r"'[^']*'",  # 单引号包围的精确短语
                r"\b\d+\b",  # 数字
                r"\b[A-Z]{2,}\b",  # 全大写缩写
                r"\b\w+\.\w+\b",  # 包含点的术语（如文件名、域名）
                r"\b\w+_\w+\b",  # 下划线连接的术语
                r"\b\w+-\w+\b",  # 连字符连接的术语
            ]

            精确匹配分数 = 0
            for 模式 in 精确匹配模式:
                匹配数 = len(re.findall(模式, 查询文本))
                精确匹配分数 += 匹配数

            # 2. 检测语义查询模式 - 识别需要深度理解的查询类型
            # 这些关键词表明用户需要概念解释、流程说明或深度分析
            语义查询关键词 = [
                "如何", "怎么", "为什么", "什么是", "解释", "描述", "比较", "区别",
                "优缺点", "原理", "方法", "步骤", "流程", "过程", "影响", "作用",
                "how", "why", "what", "explain", "describe", "compare",
                "difference", "principle", "method", "process"
            ]

            查询文本小写 = 查询文本.lower()
            语义查询分数 = sum(1 for 关键词 in 语义查询关键词 if 关键词 in 查询文本小写)

            # 3. 检测技术术语密度
            技术术语模式 = [
                r"\b[a-zA-Z]+\(\)",  # 函数调用
                r"\b[A-Z][a-zA-Z]*[A-Z][a-zA-Z]*\b",  # 驼峰命名
                r"\b\w+\.\w+\.\w+\b",  # 多级命名空间
                r"\b[a-z]+[A-Z]\w*\b",  # 小驼峰命名
            ]

            技术术语分数 = 0
            for 模式 in 技术术语模式:
                匹配数 = len(re.findall(模式, 查询文本))
                技术术语分数 += 匹配数

            # 4. 计算各种特征权重
            特征分析 = {
                "查询长度": 查询长度,
                "精确匹配分数": 精确匹配分数,
                "语义查询分数": 语义查询分数,
                "技术术语分数": 技术术语分数,
            }

            # 5. 策略选择逻辑（按优先级排序）
            if 查询长度 <= 20 and 精确匹配分数 >= 2:
                选择策略, 选择原因 = "keyword", "短查询包含精确匹配特征"
            elif 技术术语分数 >= 2:
                选择策略, 选择原因 = "keyword", "技术术语密集查询"
            elif 20 < 查询长度 <= 50 and 精确匹配分数 >= 1 and 语义查询分数 >= 1:
                选择策略, 选择原因 = "mixed", "平衡查询适合混合检索"
            elif 查询长度 > 50 and 语义查询分数 == 0:
                选择策略, 选择原因 = "mixed", "长查询需要综合检索"
            elif 语义查询分数 >= 1:
                选择策略, 选择原因 = "similarity", "语义查询特征明显"
            else:
                选择策略, 选择原因 = "similarity", "默认语义相似度检索"

            知识库服务日志器.info(
                f"智能策略选择: '{查询文本}' -> {选择策略} ({选择原因}), "
                f"特征分析: {特征分析}"
            )

            return 选择策略

        except Exception as e:
            知识库服务日志器.warning(f"智能策略选择失败: {str(e)}, 使用默认策略")
            return "similarity"

    async def _批量获取文档元数据(
        self, 文档记录id列表: List[int]
    ) -> Dict[int, Dict[str, Any]]:
        """批量获取文档元数据"""
        try:
            if not 文档记录id列表:
                return {}

            # 构建批量查询SQL
            占位符 = ",".join(["$1"] * len(文档记录id列表))
            查询SQL = f"""
            SELECT id, 文档名称, 文档类型, 文档大小, 创建时间, 更新时间,
                   文档状态, 元数据, langchain_知识库表id
            FROM langchain_知识库文档表
            WHERE id IN ({占位符})
            """

            查询结果 = await self.数据层.执行查询(查询SQL, 文档记录id列表)

            # 构建映射
            文档元数据映射 = {}
            for 行 in 查询结果:
                # 处理数据库查询结果，可能是字典或元组
                if isinstance(行, dict):
                    文档记录id = 行.get("id")
                    文档元数据映射[文档记录id] = {
                        "文档名称": 行.get("文档名称"),
                        "文档类型": 行.get("文档类型"),
                        "文档大小": 行.get("文档大小"),
                        "创建时间": 行.get("创建时间"),
                        "更新时间": 行.get("更新时间"),
                        "文档状态": 行.get("文档状态"),
                        "元数据": 行.get("元数据"),
                        "知识id": 行.get("知识id"),
                    }
                else:
                    # 假设是元组或列表
                    行_列表 = list(行)
                    文档记录id = 行_列表[0]
                    文档元数据映射[文档记录id] = {
                        "文档名称": 行_列表[1] if len(行_列表) > 1 else None,
                        "文档类型": 行_列表[2] if len(行_列表) > 2 else None,
                        "文档大小": 行_列表[3] if len(行_列表) > 3 else None,
                        "创建时间": 行_列表[4] if len(行_列表) > 4 else None,
                        "更新时间": 行_列表[5] if len(行_列表) > 5 else None,
                        "文档状态": 行_列表[6] if len(行_列表) > 6 else None,
                        "元数据": 行_列表[7] if len(行_列表) > 7 else None,
                        "知识id": 行_列表[8] if len(行_列表) > 8 else None,
                    }

            return 文档元数据映射

        except Exception as e:
            知识库服务日志器.error(f"批量获取文档元数据失败: {str(e)}")
            return {}

    # ==================== 检索配置和测试相关方法 ====================

    async def 获取检索配置(self, 知识id: int) -> Dict[str, Any]:
        """获取检索配置"""
        try:
            # 使用现有的获取知识库详情方法获取配置信息
            知识库详情 = await self.数据层.获取知识库详情(知识id)

            if 知识库详情:
                # 构建检索配置
                检索配置 = {
                    "检索策略": "similarity",  # 默认使用相似度检索
                    "相似度阈值": 0.7,  # 系统默认值，实际使用时会被关联配置覆盖
                    "最大检索数量": 5,  # 系统默认值，实际使用时会被关联配置覆盖
                    "嵌入模型": 知识库详情.get("嵌入模型名称"),
                    "嵌入模型id": 知识库详情.get("嵌入模型"),
                    "向量维度": 知识库详情.get("向量维度", 1024),
                    "模型提供商": 知识库详情.get("嵌入模型提供商"),
                    "支持语言": "中文,英文",  # 默认支持语言
                    # 查询优化配置
                    "启用查询优化": bool(知识库详情.get("默认启用查询优化", 0)),
                    "查询优化策略": 知识库详情.get("默认查询优化策略", "rewrite"),
                    "查询优化模型id": 知识库详情.get("默认查询优化模型id"),
                    "查询优化提示词": 知识库详情.get("默认查询优化提示词", ""),
                }

                # 如果没有配置嵌入模型，尝试获取默认模型
                if not 检索配置["嵌入模型id"]:
                    默认模型结果 = await self.获取默认嵌入模型()
                    if 默认模型结果["success"]:
                        默认模型 = 默认模型结果["data"]
                        检索配置.update(
                            {
                                "嵌入模型": 默认模型["模型名称"],
                                "嵌入模型id": 默认模型["id"],
                                "向量维度": 默认模型["向量维度"],
                                "模型提供商": 默认模型["供应商名称"],
                                "支持语言": 默认模型["支持语言"],
                            }
                        )
                    else:
                        # 如果没有可用的嵌入模型，使用关键词检索
                        检索配置.update(
                            {
                                "检索策略": "keyword",
                                "嵌入模型": None,
                                "嵌入模型id": None,
                                "向量维度": 0,
                                "模型提供商": "",
                                "支持语言": "",
                                "警告": "未配置嵌入模型，将使用关键词检索",
                            }
                        )

                知识库服务日志器.info(f"获取知识库 {知识id} 的检索配置成功")
                return {"success": True, "data": 检索配置}
            else:
                知识库服务日志器.warning(f"知识库不存在: {知识id}")
                return {"success": False, "error": f"知识库不存在: {知识id}"}

        except Exception as e:
            知识库服务日志器.error(f"获取检索配置失败: {str(e)}")
            return {"success": False, "error": f"获取检索配置失败: {str(e)}"}

    async def 获取智能检索配置(
        self, 知识id: int, 查询文本: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取智能检索配置：支持根据查询文本自动选择最适合的检索策略"""
        try:
            # 先获取基础检索配置
            基础配置结果 = await self.获取检索配置(知识id)
            if not 基础配置结果.get("success"):
                return 基础配置结果

            检索配置 = 基础配置结果["data"].copy()

            # 如果提供了查询文本，使用智能策略选择
            if 查询文本:
                知识库详情 = await self.数据层.获取知识库详情(知识id)
                智能策略 = self._智能选择检索策略(查询文本, 知识库详情)

                # 更新检索策略
                原始策略 = 检索配置.get("检索策略", "similarity")
                检索配置["检索策略"] = 智能策略
                检索配置["原始策略"] = 原始策略
                检索配置["策略选择方式"] = "智能选择"

                知识库服务日志器.info(
                    f"智能检索配置: 知识库{知识id}, 查询'{查询文本}', "
                    f"策略 {原始策略} -> {智能策略}"
                )
            else:
                检索配置["策略选择方式"] = "默认配置"

            return {"success": True, "data": 检索配置}

        except Exception as e:
            知识库服务日志器.error(f"获取智能检索配置失败: {str(e)}")
            return {"success": False, "error": f"获取智能检索配置失败: {str(e)}"}

    async def 更新检索配置(
        self, 知识id: int, 配置数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """更新检索配置"""
        try:
            # 简化更新检索配置实现
            # TODO: 实现具体的检索配置更新逻辑，知识id: {知识id}, 配置数据: {配置数据}
            更新成功 = True

            if 更新成功:
                return {"success": True, "message": "检索配置更新成功"}
            else:
                return {"success": False, "error": "检索配置更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"更新检索配置失败: {str(e)}")
            return {"success": False, "error": f"更新检索配置失败: {str(e)}"}

    async def 直接知识库检索(
        self, 知识id: int, 查询文本: str, 检索参数: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """直接知识库检索 - 纯粹的知识库向量检索测试，不依赖智能体"""
        try:
            if 检索参数 is None:
                检索参数 = {}

            # 获取检索配置
            配置结果 = await self.获取检索配置(知识id)
            if not 配置结果.get("success"):
                return {"success": False, "error": "无法获取检索配置"}

            检索配置 = 配置结果["data"]

            # 如果指定了嵌入模型id，使用指定的模型
            if 检索参数.get("嵌入模型id"):
                指定模型结果 = await self._获取指定嵌入模型(检索参数["嵌入模型id"])
                if 指定模型结果["success"]:
                    指定模型 = 指定模型结果["data"]
                    检索配置.update(
                        {
                            "嵌入模型": 指定模型["模型名称"],
                            "嵌入模型id": 指定模型["id"],
                            "向量维度": 指定模型["向量维度"],
                            "模型提供商": 指定模型["供应商名称"],
                        }
                    )

            # 优先使用传入的检索参数，其次使用检索配置，最后使用系统默认值
            最大检索数量 = 检索参数.get("最大检索数量", 检索配置.get("最大检索数量", 5))
            相似度阈值 = 检索参数.get("相似度阈值", 检索配置.get("相似度阈值", 0.7))

            # 获取知识库的向量化文档 - 简化实现
            # TODO: 实现具体的向量文档获取逻辑
            向量文档列表 = []

            if not 向量文档列表:
                return {
                    "success": True,
                    "data": {
                        "查询文本": 查询文本,
                        "检索结果": [],
                        "结果数量": 0,
                        "检索参数": 检索参数,
                        "检索配置": 检索配置,
                        "message": "知识库中没有向量化文档，请先进行向量化",
                    },
                }

            # 执行相似度检索
            检索结果 = await self._执行相似度检索(
                查询文本, 向量文档列表, 最大检索数量, 相似度阈值, 检索配置
            )

            知识库服务日志器.info(
                f"检索测试成功: 知识库 {知识id}, 查询: {查询文本}, 结果数量: {len(检索结果)}"
            )
            return {
                "success": True,
                "data": {
                    "查询文本": 查询文本,
                    "检索结果": 检索结果,
                    "结果数量": len(检索结果),
                    "检索参数": 检索参数,
                    "检索配置": 检索配置,
                    "message": f"基于向量相似度检索找到 {len(检索结果)} 个相关文档片段",
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"测试检索失败: {str(e)}")
            return {"success": False, "error": f"测试检索失败: {str(e)}"}

    async def 实时参数检索测试(
        self,
        知识库列表: List[int],
        测试查询: str,
        检索配置: Dict[str, Any],
        查询优化配置: Optional[Dict[str, Any]] = None,
        测试模式: str = "standard",
    ) -> Dict[str, Any]:
        """实时参数检索测试 - 完全使用传入参数，不依赖数据库配置"""
        try:
            知识库服务日志器.info(
                f"🧪 开始实时参数检索测试: 知识库数量={len(知识库列表)}, 查询='{测试查询}'"
            )

            # 验证知识库列表
            if not 知识库列表:
                return {"success": False, "error": "知识库列表不能为空"}

            # 解析检索配置
            检索策略 = 检索配置.get("检索策略", "vector")
            嵌入模型 = 检索配置.get("嵌入模型")
            相似度阈值 = float(检索配置.get("相似度阈值", 0.7))
            最大检索数量 = int(检索配置.get("最大检索数量", 10))
            # 注意：分块相关参数在当前实现中暂未使用，但保留以备将来扩展
            # 分块大小 = int(检索配置.get("分块大小", 1000))
            # 分块重叠 = int(检索配置.get("分块重叠", 200))
            # 分块策略 = 检索配置.get("分块策略", "recursive")

            知识库服务日志器.debug(
                f"📊 检索配置: 策略={检索策略}, 模型={嵌入模型}, 阈值={相似度阈值}, 数量={最大检索数量}"
            )

            # 获取嵌入模型配置
            if not 嵌入模型:
                return {"success": False, "error": "检索配置中缺少嵌入模型"}

            # 通过模型名称获取模型配置
            模型配置结果 = await self._通过模型名称获取配置(嵌入模型)
            if not 模型配置结果["success"]:
                return {
                    "success": False,
                    "error": f"无法获取嵌入模型配置: {模型配置结果.get('error')}",
                }

            模型配置 = 模型配置结果["data"]

            # 收集所有知识库的检索结果
            所有检索结果 = []
            知识库检索详情 = []
            # 收集查询优化信息（只保留最后一次的优化信息）
            最终查询优化信息 = None

            for 知识id in 知识库列表:
                try:
                    知识库服务日志器.debug(f"🔍 开始检索知识库 {知识id}")

                    # 验证知识库存在性
                    知识库验证 = await self._验证知识库存在(知识id)
                    if not 知识库验证["success"]:
                        知识库检索详情.append(
                            {
                                "知识id": 知识id,
                                "状态": "失败",
                                "错误": 知识库验证.get("error", "知识库不存在"),
                                "结果数量": 0,
                            }
                        )
                        continue

                    # 注意：检索参数直接传递给检索方法，无需构建中间变量

                    # 处理查询优化配置
                    当前查询优化配置 = None
                    if 查询优化配置 and (
                        查询优化配置.get("启用") or 查询优化配置.get("启用查询优化")
                    ):
                        知识库服务日志器.info(
                            f"🔧 测试多知识库检索：使用查询优化配置 {查询优化配置}"
                        )
                        当前查询优化配置 = {
                            "启用": True,
                            "优化策略": 查询优化配置.get("优化策略", "rewrite"),
                            "优化模型id": 查询优化配置.get("优化模型id")
                            or 查询优化配置.get("优化模型"),
                            "提示词模板": 查询优化配置.get("提示词模板", "")
                            or 查询优化配置.get("优化提示词", ""),
                        }
                        知识库服务日志器.info(
                            f"🔧 测试多知识库检索：映射后的查询优化配置 {当前查询优化配置}"
                        )

                    # 执行检索 - 根据策略选择不同的检索方法
                    if 检索策略 == "mixed":
                        检索结果 = await self.混合检索(
                            知识id, 测试查询, 最大检索数量, 相似度阈值
                        )
                    elif 检索策略 == "keyword":
                        检索结果 = await self.PostgreSQL关键词检索(
                            知识id=知识id,
                            查询文本=测试查询,
                            最大数量=最大检索数量,
                            语言配置="simple",  # 默认使用simple配置，支持中英文
                        )
                    else:
                        # 默认使用向量检索（similarity策略）
                        检索结果 = await self.PostgreSQL向量检索(
                            知识id=知识id,
                            查询文本=测试查询,
                            最大数量=最大检索数量,
                            相似度阈值=相似度阈值,
                            查询优化配置=当前查询优化配置,
                        )

                    if 检索结果.get("success"):
                        检索文档列表 = 检索结果.get("检索结果", [])
                        # 为每个结果添加知识id标识
                        for 文档 in 检索文档列表:
                            文档["知识id"] = 知识id
                        所有检索结果.extend(检索文档列表)

                        # 收集查询优化信息（如果有）
                        if 检索结果.get("查询优化信息"):
                            最终查询优化信息 = 检索结果.get("查询优化信息")

                        知识库检索详情.append(
                            {
                                "知识id": 知识id,
                                "状态": "成功",
                                "结果数量": len(检索文档列表),
                                "使用策略": 检索策略,
                                "相似度阈值": 相似度阈值,
                            }
                        )

                        知识库服务日志器.info(
                            f"✅ 知识库 {知识id} 检索成功: {len(检索文档列表)} 个结果"
                        )
                    else:
                        知识库检索详情.append(
                            {
                                "知识id": 知识id,
                                "状态": "失败",
                                "错误": 检索结果.get("error", "检索失败"),
                                "结果数量": 0,
                            }
                        )

                except Exception as e:
                    知识库服务日志器.warning(f"知识库 {知识id} 检索异常: {str(e)}")
                    知识库检索详情.append(
                        {
                            "知识id": 知识id,
                            "状态": "异常",
                            "错误": str(e),
                            "结果数量": 0,
                        }
                    )

            # 按相似度分数排序（如果有的话）
            if 所有检索结果:
                所有检索结果.sort(key=lambda x: x.get("相似度分数", 0), reverse=True)

            # 构建响应数据
            响应数据 = {
                "检索结果": 所有检索结果,
                "总结果数量": len(所有检索结果),
                "知识库检索详情": 知识库检索详情,
                "查询优化信息": 最终查询优化信息,
                "测试配置": {
                    "测试查询": 测试查询,
                    "检索配置": 检索配置,
                    "查询优化配置": 查询优化配置,
                    "测试模式": 测试模式,
                },
                "测试时间": datetime.now().isoformat(),
            }

            # 如果是调试模式，添加更多详细信息
            if 测试模式 == "debug":
                响应数据["调试信息"] = {
                    "嵌入模型配置": 模型配置,
                    "知识库数量": len(知识库列表),
                    "成功知识库数量": len(
                        [d for d in 知识库检索详情 if d["状态"] == "成功"]
                    ),
                }

            知识库服务日志器.info(
                f"🎯 实时参数检索测试完成: 总结果数量={len(所有检索结果)}, "
                f"成功知识库={len([d for d in 知识库检索详情 if d['状态'] == '成功'])}/{len(知识库列表)}"
            )

            return {"success": True, "data": 响应数据}

        except Exception as e:
            知识库服务日志器.error(f"实时参数检索测试异常: {str(e)}", exc_info=True)
            return {"success": False, "error": f"检索测试失败: {str(e)}"}

    async def 获取知识库向量文档(self, 知识id: int) -> List[Dict[str, Any]]:
        """从PostgreSQL获取知识库的向量文档"""
        try:
            # 向量统计功能已整合到知识库服务中
            统计信息 = {"success": True, "向量数量": 0}  # 简化处理

            if 统计信息.get("success"):
                return [
                    {
                        "知识id": 知识id,
                        "文档数量": 统计信息.get("文档数量", 0),
                        "向量数量": 统计信息.get("向量数量", 0),
                        "已完成向量数": 统计信息.get("已完成向量数", 0),
                        "存储类型": "PostgreSQL",
                        "状态": "正常",
                    }
                ]
            else:
                return []

        except Exception as e:
            知识库服务日志器.error(f"获取PostgreSQL向量文档失败: {str(e)}")
            return []

    async def 获取向量存储状态(self, 知识id: int) -> Dict[str, Any]:
        """获取知识库的PostgreSQL向量存储状态"""
        try:
            # 获取知识库详情
            知识库详情 = await self.数据层.获取知识库详情(知识id)
            if not 知识库详情:
                return {"success": False, "error": "知识库不存在"}

            # 向量统计功能已整合到知识库服务中
            统计信息 = {"success": True, "向量数量": 0}  # 简化处理

            if 统计信息.get("success"):
                return {
                    "success": True,
                    "data": {
                        "知识id": 知识id,
                        "知识库名称": 知识库详情.get("知识库名称"),
                        "存储类型": "PostgreSQL",
                        "向量存储状态": "活跃",
                        "文档数量": 统计信息.get("文档数量", 0),
                        "向量数量": 统计信息.get("向量数量", 0),
                        "已完成向量数": 统计信息.get("已完成向量数", 0),
                        "最后更新时间": 知识库详情.get("更新时间"),
                    },
                }
            else:
                return {
                    "success": False,
                    "error": 统计信息.get("error", "获取向量存储状态失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"获取向量存储状态失败: {str(e)}")
            return {"success": False, "error": f"获取向量存储状态失败: {str(e)}"}

    async def 清空知识库向量数据(self, 知识id: int) -> Dict[str, Any]:
        """清空知识库的PostgreSQL向量数据"""
        try:
            # 向量清空功能已整合到知识库服务中
            知识库服务日志器.info(f"知识库 {知识id} 的PostgreSQL向量数据清空功能已简化")
            return {"success": True, "message": "知识库向量数据清空功能已简化"}

        except Exception as e:
            知识库服务日志器.error(f"清空知识库向量数据失败: {str(e)}")
            return {"success": False, "error": f"清空知识库向量数据失败: {str(e)}"}

    async def 验证并修复文档状态(self, 文档记录id: int) -> Dict[str, Any]:
        """验证并修复文档向量状态 - 确保状态一致性"""
        try:
            知识库服务日志器.info(f"🔍 开始验证文档状态一致性: 文档id {文档记录id}")

            # 验证状态一致性
            验证结果 = await self.数据层.验证文档向量状态一致性(文档记录id)

            if not 验证结果.get("success"):
                return {
                    "success": False,
                    "error": f"状态验证失败: {验证结果.get('error', '未知错误')}",
                }

            一致性数据 = 验证结果["data"]

            # 如果状态一致，直接返回
            if 一致性数据["状态一致"] and 一致性数据["当前状态正确"]:
                知识库服务日志器.info(f"✅ 文档状态一致性验证通过: 文档id {文档记录id}")
                return {
                    "success": True,
                    "message": "文档状态一致，无需修复",
                    "验证结果": 一致性数据,
                }

            # 需要修复状态
            知识库服务日志器.warning(
                f"⚠️ 发现状态不一致: 文档id {文档记录id}, 不一致项: {一致性数据['不一致项']}"
            )

            # 修复状态
            建议状态 = 一致性数据["建议状态"]
            有效向量数量 = 一致性数据["有效向量数量"]

            修复成功 = await self.数据层.更新文档向量状态(文档记录id, 建议状态)

            if 修复成功:
                知识库服务日志器.info(
                    f"✅ 文档状态修复成功: 文档id {文档记录id}, "
                    f"状态: {一致性数据['文档向量状态']} -> {建议状态}, "
                    f"有效向量数量: {有效向量数量}"
                )

                return {
                    "success": True,
                    "message": "文档状态修复成功",
                    "修复前状态": 一致性数据["文档向量状态"],
                    "修复后状态": 建议状态,
                    "有效向量数量": 有效向量数量,
                    "验证结果": 一致性数据,
                }
            else:
                return {
                    "success": False,
                    "error": "状态修复失败",
                    "验证结果": 一致性数据,
                }

        except Exception as e:
            知识库服务日志器.error(f"验证并修复文档状态失败: {str(e)}")
            return {"success": False, "error": f"验证修复失败: {str(e)}"}

    async def 批量验证知识库状态(self, 知识id: int) -> Dict[str, Any]:
        """批量验证知识库中所有文档的状态一致性"""
        try:
            知识库服务日志器.info(f"🔍 开始批量验证知识库状态: 知识id {知识id}")

            # 获取知识库中的所有文档
            文档列表, _ = await self.数据层.获取知识库文档列表(
                知识id,
                {"页码": 1, "每页数量": 1000},  # 获取大量文档
            )

            验证统计 = {
                "总文档数": len(文档列表),
                "一致文档数": 0,
                "不一致文档数": 0,
                "修复成功数": 0,
                "修复失败数": 0,
                "详细结果": [],
            }

            for 文档 in 文档列表:
                文档id = 文档["id"]

                try:
                    验证修复结果 = await self.验证并修复文档状态(文档id)

                    if 验证修复结果.get("success"):
                        if "无需修复" in 验证修复结果.get("message", ""):
                            验证统计["一致文档数"] += 1
                        else:
                            验证统计["不一致文档数"] += 1
                            验证统计["修复成功数"] += 1
                    else:
                        验证统计["不一致文档数"] += 1
                        验证统计["修复失败数"] += 1

                    验证统计["详细结果"].append(
                        {
                            "文档id": 文档id,
                            "文档名称": 文档.get("文档名称", ""),
                            "验证结果": 验证修复结果,
                        }
                    )

                except Exception as doc_error:
                    验证统计["修复失败数"] += 1
                    验证统计["详细结果"].append(
                        {
                            "文档id": 文档id,
                            "文档名称": 文档.get("文档名称", ""),
                            "验证结果": {"success": False, "error": str(doc_error)},
                        }
                    )

            知识库服务日志器.info(
                f"✅ 批量验证完成: 知识id {知识id}, "
                f"总计 {验证统计['总文档数']} 个文档, "
                f"一致 {验证统计['一致文档数']} 个, "
                f"修复成功 {验证统计['修复成功数']} 个, "
                f"修复失败 {验证统计['修复失败数']} 个"
            )

            return {"success": True, "验证统计": 验证统计}

        except Exception as e:
            知识库服务日志器.error(f"批量验证知识库状态失败: {str(e)}")
            return {"success": False, "error": f"批量验证失败: {str(e)}"}

    async def _执行相似度检索(
        self,
        查询文本: str,
        向量文档列表: List[Dict[str, Any]],
        最大检索数量: int,
        相似度阈值: float,
        检索配置: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """执行相似度检索 - 基于LangChain similarity_search模式"""
        try:
            # 简化的相似度计算（实际应该使用嵌入模型）
            检索结果 = []
            查询关键词 = 查询文本.lower().split()

            for 文档 in 向量文档列表:
                文档内容 = str(文档.get("内容", "")).lower()

                # 计算简单的关键词匹配分数
                匹配分数 = 0.0
                for 关键词 in 查询关键词:
                    if 关键词 in 文档内容:
                        匹配分数 += 1.0 / len(查询关键词)

                # 只保留超过阈值的结果
                if 匹配分数 >= 相似度阈值:
                    检索结果.append(
                        {
                            "序号": len(检索结果) + 1,
                            "内容": 文档.get("内容", "")[:300] + "..."
                            if len(文档.get("内容", "")) > 300
                            else 文档.get("内容", ""),
                            "元数据": {
                                "文档UUID": 文档.get("uuid", ""),
                                "创建时间": str(文档.get("创建时间", "")),
                                **文档.get("元数据", {}),
                            },
                            "相似度分数": round(匹配分数, 3),
                            "检索策略": 检索配置.get("检索策略", "similarity"),
                        }
                    )

            # 按相似度分数排序
            检索结果.sort(key=lambda x: x["相似度分数"], reverse=True)

            # 限制返回数量
            return 检索结果[:最大检索数量]

        except Exception as e:
            知识库服务日志器.error(f"执行相似度检索失败: {str(e)}")
            return []

    # ==================== 智能体检索测试相关方法 ====================

    async def 智能体检索测试(
        self, 智能体id: int, 查询文本: str, 检索参数: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """智能体检索测试 - 测试智能体通过RAG引擎检索关联知识库的能力"""
        try:
            if 检索参数 is None:
                检索参数 = {}

            # RAG功能已整合到知识库服务中，无需外部初始化

            # 获取智能体关联配置 - 直接从数据库获取
            智能体关联配置 = await self._获取智能体RAG配置(智能体id)

            # 合并配置：传入参数优先级高于关联配置
            合并后检索参数 = {**智能体关联配置, **检索参数}

            # 执行检索 - 支持智能多层检索
            最大检索数量 = 合并后检索参数.get("最大检索数量", 5)
            相似度阈值 = 合并后检索参数.get("相似度阈值", 0.7)
            启用智能检索 = 合并后检索参数.get("启用智能多层检索", True)
            最小结果数量 = 合并后检索参数.get("最小结果数量", 3)

            # 提取查询优化配置
            查询优化配置 = 合并后检索参数.get("查询优化配置", {})

            # 检查是否使用手动优化查询
            手动优化查询 = 检索参数.get("手动优化查询")
            跳过第一阶段 = 检索参数.get("跳过第一阶段", False)

            if 手动优化查询 and 跳过第一阶段:
                # 直接使用手动优化查询进行检索，跳过两阶段流程
                知识库服务日志器.info(f"🎯 使用手动优化查询直接检索: '{手动优化查询}'")
                查询文本 = 手动优化查询
                # 禁用查询优化配置，避免再次优化
                检索参数["查询优化配置"] = {}

            知识库服务日志器.info(
                f"🔍 开始检索: 查询='{查询文本}', 最大数量={最大检索数量}, "
                f"相似度阈值={相似度阈值}, 智能检索={'启用' if 启用智能检索 else '禁用'}"
            )

            # 获取智能体关联的知识库列表
            关联知识库 = await LangChain智能体数据层实例.获取智能体关联知识库(智能体id)

            if not 关联知识库:
                知识库服务日志器.warning(f"智能体 {智能体id} 未关联任何知识库")
                return {
                    "success": False,
                    "error": "智能体未关联任何知识库",
                    "data": {"检索结果": []},
                }

            # 收集所有知识库的检索结果
            所有检索结果 = []
            # 收集查询优化信息（只保留最后一次的优化信息）
            最终查询优化信息 = None

            for 关联信息 in 关联知识库:
                知识id = 关联信息.get("知识id")
                if not 知识id:
                    continue

                try:
                    # 使用合并后的查询优化配置
                    当前查询优化配置 = 查询优化配置

                    # 获取当前知识库的检索策略
                    当前检索策略 = 关联信息.get("检索策略", "similarity")
                    知识库服务日志器.debug(
                        f"知识库 {知识id} 使用检索策略: {当前检索策略}"
                    )

                    # 获取当前知识库的嵌入模型ID
                    当前嵌入模型id = None
                    for 知识库配置 in 合并后检索参数.get("知识库配置", []):
                        if 知识库配置["知识库id"] == 知识id:
                            当前嵌入模型id = 知识库配置["嵌入模型id"]
                            break

                    # 根据检索策略选择检索方法
                    if 当前检索策略 == "keyword":
                        # 使用关键词检索
                        检索结果 = await self.PostgreSQL关键词检索(
                            知识id=知识id,
                            查询文本=查询文本,
                            最大数量=最大检索数量,
                            语言配置="simple",  # 默认使用simple配置，支持中英文
                        )
                    elif 当前检索策略 == "mixed":
                        # 使用混合检索
                        检索结果 = await self.混合检索(
                            知识id, 查询文本, 最大检索数量, 相似度阈值
                        )
                    else:
                        # 默认使用向量检索（similarity策略）
                        if 启用智能检索:
                            # 使用智能多层检索
                            检索结果 = await self.智能多层检索(
                                知识id=知识id,
                                查询文本=查询文本,
                                最大数量=最大检索数量,
                                相似度阈值=相似度阈值,
                                最小结果数量=最小结果数量,
                                查询优化配置=当前查询优化配置,
                                嵌入模型id=当前嵌入模型id,  # 传递嵌入模型ID
                            )
                        else:
                            # 使用传统向量检索
                            检索结果 = await self.PostgreSQL向量检索(
                                知识id=知识id,
                                查询文本=查询文本,
                                最大数量=最大检索数量,
                                相似度阈值=相似度阈值,
                                查询优化配置=当前查询优化配置,
                                嵌入模型id=当前嵌入模型id,  # 传递嵌入模型ID
                            )

                    if 检索结果.get("success"):
                        检索文档列表 = 检索结果.get("检索结果", [])
                        # 为每个结果添加知识id标识
                        for 文档 in 检索文档列表:
                            文档["知识id"] = 知识id
                        所有检索结果.extend(检索文档列表)

                        # 收集查询优化信息（如果有）
                        if 检索结果.get("查询优化信息"):
                            最终查询优化信息 = 检索结果.get("查询优化信息")

                        知识库服务日志器.info(
                            f"✅ 知识库 {知识id} 检索成功: {len(检索文档列表)} 个结果"
                        )

                except Exception as e:
                    知识库服务日志器.warning(f"知识库 {知识id} 检索失败: {str(e)}")
                    continue

            知识库服务日志器.info(
                f"📊 检索完成: 总结果数量={len(所有检索结果)}, "
                f"智能检索={'启用' if 启用智能检索 else '禁用'}"
            )

            # 处理检索结果
            if 所有检索结果:
                # 按相似度分数排序
                所有检索结果.sort(key=lambda x: x.get("相似度分数", 0), reverse=True)

                # 格式化结果
                格式化结果 = []
                for i, 结果 in enumerate(所有检索结果):
                    格式化结果.append(
                        {
                            "序号": i + 1,
                            "文档内容": 结果.get("分块内容", "")[:500] + "..."
                            if len(结果.get("分块内容", "")) > 500
                            else 结果.get("分块内容", ""),
                            "元数据": 结果.get("元数据", {}),
                            "相似度分数": 结果.get("相似度分数", 0),
                            "原始距离分数": 结果.get("原始距离分数", 0),
                            "检索方式": 结果.get("检索方式", "智能多层检索"),
                            "知识id": 结果.get("知识id", 0),
                        }
                    )

                # 构建返回结果
                返回结果 = {
                    "success": True,
                    "data": {
                        "检索结果": 格式化结果,
                        "检索参数": 检索参数,
                        "智能体id": 智能体id,
                        "查询文本": 查询文本,
                        "使用智能检索": 启用智能检索,
                        "查询优化信息": 最终查询优化信息,
                        **(
                            {
                                "手动优化查询": 手动优化查询,
                                "检索模式": "手动优化查询",
                            }
                            if (手动优化查询 and 跳过第一阶段)
                            else {"检索模式": "标准检索"}
                        ),
                    },
                }

                知识库服务日志器.info(
                    f"✅ 智能体检索成功: {len(格式化结果)}个结果, 智能检索={'启用' if 启用智能检索 else '禁用'}"
                )

                return 返回结果
            else:
                return {
                    "success": True,
                    "data": {
                        "检索结果": [],
                        "智能体id": 智能体id,
                        "查询文本": 查询文本,
                        "使用智能检索": 启用智能检索,
                        "查询优化信息": 最终查询优化信息,
                    },
                }

        except Exception as e:
            知识库服务日志器.error(f"智能体检索测试失败: {str(e)}")
            return {"success": False, "error": f"智能体检索测试失败: {str(e)}"}

    async def 获取智能体关联知识库(self, 智能体id: int) -> Dict[str, Any]:
        """获取智能体关联的知识库列表 - 使用关联表"""
        try:
            # 获取智能体基本信息
            智能体详情 = await LangChain智能体数据层实例.获取智能体详情完整(智能体id)

            if not 智能体详情:
                return {"success": False, "error": "智能体不存在"}

            # 通过关联表获取知识库列表
            关联查询SQL = """
            SELECT
                akr.langchain_知识库表id as 知识id,
                akr.权重,
                akr.检索策略,
                akr.最大检索数量,
                akr.相似度阈值,
                akr.状态 as 关联状态
            FROM langchain_智能体知识库关联表 akr
            WHERE akr.langchain_智能体配置表id = $1 AND akr.状态 = 'active'
            ORDER BY akr.权重 DESC, akr.id ASC
            """

            关联结果 = await self.数据层.数据库连接池.执行查询(关联查询SQL, (智能体id,))

            # 获取知识库详细信息
            知识库详情列表 = []
            for 关联记录 in 关联结果 or []:
                知识id = 关联记录["知识id"]
                知识库详情 = await self.数据层.获取知识库详情(知识id)
                if 知识库详情:
                    # 合并关联信息和知识库详情
                    知识库详情.update(
                        {
                            "权重": 关联记录["权重"],
                            "检索策略": 关联记录["检索策略"],
                            "最大检索数量": 关联记录["最大检索数量"],
                            "相似度阈值": 关联记录["相似度阈值"],
                            "关联状态": 关联记录["关联状态"],
                        }
                    )
                    知识库详情列表.append(知识库详情)

            return {
                "success": True,
                "data": {
                    "智能体id": 智能体id,
                    "智能体名称": 智能体详情.get("智能体名称", ""),
                    "关联知识库": 知识库详情列表,
                    "知识库数量": len(知识库详情列表),
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"获取智能体关联知识库失败: {str(e)}")
            return {"success": False, "error": f"获取智能体关联知识库失败: {str(e)}"}

    # ==================== 嵌入模型管理相关方法 ====================

    async def 获取可用嵌入模型列表(self) -> Dict[str, Any]:
        """获取所有可用的嵌入模型列表"""
        try:
            # 查询所有可用的嵌入模型
            查询SQL = """
            SELECT
                id,
                模型名称,
                模型类型,
                显示名称,
                提供商,
                api密钥,
                api基础url,
                最大令牌数,
                算力消耗
            FROM langchain_模型配置表
            WHERE 模型类型 LIKE '%embedding%'
            ORDER BY 提供商, 模型名称
            """

            结果 = await self.数据层.数据库连接池.执行查询(查询SQL)

            嵌入模型列表 = []
            for 模型信息 in 结果:
                # 由于数据库表中没有模型参数字段，使用默认值
                模型参数 = {}

                嵌入模型列表.append(
                    {
                        "id": 模型信息["id"],
                        "模型名称": 模型信息["模型名称"],
                        "模型类型": 模型信息["模型类型"],
                        "显示名称": 模型信息["显示名称"],
                        "提供商": 模型信息["提供商"],
                        "向量维度": 模型参数.get("embedding_dimension", 1536),
                        "支持语言": 模型参数.get("supported_languages", "中文/英文"),
                        "最大令牌数": 模型信息.get("最大令牌数"),
                        "算力消耗": 模型信息.get("算力消耗"),
                        "启用状态": 1,  # 由于数据库表中没有启用状态字段，默认为启用
                        "api基础url": 模型信息.get("api基础url"),
                    }
                )

            知识库服务日志器.info(
                f"获取可用嵌入模型列表成功: {len(嵌入模型列表)} 个模型"
            )
            return {"success": True, "data": 嵌入模型列表}

        except Exception as e:
            知识库服务日志器.error(f"获取可用嵌入模型列表失败: {str(e)}")
            return {"success": False, "error": f"获取可用嵌入模型列表失败: {str(e)}"}

    async def 获取可用AI模型列表(self) -> Dict[str, Any]:
        """获取可用的AI对话模型列表"""
        try:
            查询SQL = """
            SELECT
                id,
                模型名称,
                模型类型,
                显示名称,
                提供商
            FROM langchain_模型配置表
            WHERE 模型类型 LIKE '%chat%'
            ORDER BY id DESC
            """

            结果 = await self.数据层.数据库连接池.执行查询(查询SQL)

            if 结果:
                知识库服务日志器.info(f"获取到 {len(结果)} 个可用AI模型")
                return {"success": True, "data": 结果}
            else:
                知识库服务日志器.warning("没有找到可用的AI模型")
                return {"success": True, "data": []}

        except Exception as e:
            知识库服务日志器.error(f"获取可用AI模型列表失败: {str(e)}")
            return {"success": False, "error": f"获取可用AI模型列表失败: {str(e)}"}

    async def 获取默认嵌入模型(self) -> Dict[str, Any]:
        """获取默认的嵌入模型"""
        try:
            # 获取第一个可用的嵌入模型作为默认模型
            模型列表结果 = await self.获取可用嵌入模型列表()

            if 模型列表结果["success"] and 模型列表结果["data"]:
                默认模型 = 模型列表结果["data"][0]
                return {"success": True, "data": 默认模型}
            else:
                return {"success": False, "error": "没有可用的嵌入模型"}

        except Exception as e:
            知识库服务日志器.error(f"获取默认嵌入模型失败: {str(e)}")
            return {"success": False, "error": f"获取默认嵌入模型失败: {str(e)}"}

    # AI模型相关方法移除 - 应该在智能体服务中处理

    async def _获取指定嵌入模型(self, 模型id: int) -> Dict[str, Any]:
        """根据id获取指定的嵌入模型"""
        try:
            查询SQL = """
            SELECT
                id,
                模型名称,
                模型类型,
                显示名称,
                提供商
            FROM langchain_模型配置表
            WHERE id = $1
            AND 模型类型 LIKE '%embedding%'
            """

            结果 = await self.数据层.数据库连接池.执行查询(查询SQL, (模型id,))

            if 结果:
                模型信息 = 结果[0]

                return {
                    "success": True,
                    "data": {
                        "id": 模型信息["id"],
                        "模型名称": 模型信息["模型名称"],
                        "显示名称": 模型信息["显示名称"],
                        "提供商": 模型信息["提供商"],
                        "支持语言": "中文/英文",
                        "模型参数": {},
                    },
                }
            else:
                return {"success": False, "error": f"未找到ID为 {模型id} 的嵌入模型"}

        except Exception as e:
            知识库服务日志器.error(f"获取指定嵌入模型失败: {str(e)}")
            return {"success": False, "error": f"获取指定嵌入模型失败: {str(e)}"}

    # ==================== PostgreSQL向量存储管理方法 ====================

    async def _检查分块策略适配性(self, 文件大小: int, 分块策略: str) -> Dict[str, Any]:
        """
        检查分块策略与文件大小的适配性

        参数:
            文件大小: 文件大小（字节）
            分块策略: 分块策略名称

        返回:
            Dict: 包含适配性检查结果
        """
        try:
            # 行级精准分块的文件大小限制
            if 分块策略 == "行级精准分块":
                if 文件大小 > self.行级分块_最大文件大小:
                    预估分块数 = 文件大小 // self.行级分块_平均行字符数
                    文件大小MB = 文件大小 / 1024 / 1024

                    return {
                        "适配": False,
                        "错误信息": f"文件过大（{文件大小MB:.1f}MB），不适合行级精准分块。预估会产生{预估分块数}个分块，严重影响数据库性能。",
                        "建议": [
                            "使用'语义优化分块'处理结构化数据",
                            "使用'智能递归分块'处理通用文档",
                            "将大文件拆分为多个小文件后再使用行级分块",
                        ],
                    }
                elif 文件大小 > self.行级分块_警告文件大小:
                    预估分块数 = 文件大小 // self.行级分块_平均行字符数
                    文件大小KB = 文件大小 / 1024

                    知识库服务日志器.warning(
                        f"⚠️ 行级分块性能警告: 文件{文件大小KB:.0f}KB，预估{预估分块数}个分块"
                    )

                    return {
                        "适配": True,
                        "警告": f"文件较大（{文件大小KB:.0f}KB），建议控制在20KB以内获得最佳性能",
                        "预估分块数": 预估分块数,
                    }
                else:
                    预估分块数 = 文件大小 // self.行级分块_平均行字符数
                    知识库服务日志器.info(
                        f"✅ 行级分块适配性良好: 文件{文件大小 / 1024:.0f}KB，预估{预估分块数}个分块"
                    )

                    return {"适配": True, "预估分块数": 预估分块数}

            # 其他分块策略暂无特殊限制
            return {"适配": True}

        except Exception as e:
            知识库服务日志器.error(f"检查分块策略适配性失败: {str(e)}")
            return {"适配": True}  # 出错时允许继续，避免阻塞正常流程

    async def _通过模型名称获取配置(self, 模型名称: str) -> Dict[str, Any]:
        """通过模型名称获取模型配置"""
        try:
            # 调用模型数据层获取模型配置
            模型配置 = await LangChain模型数据层实例.根据模型名称获取配置(模型名称)
            if 模型配置:
                return {"success": True, "data": 模型配置}
            else:
                return {"success": False, "error": f"未找到模型 {模型名称} 的配置"}
        except Exception as e:
            知识库服务日志器.error(f"获取模型配置失败: {str(e)}")
            return {"success": False, "error": f"获取模型配置失败: {str(e)}"}

    async def _验证知识库存在(self, 知识id: int) -> Dict[str, Any]:
        """验证知识库是否存在"""
        try:
            # 调用数据层获取知识库详情
            知识库详情 = await self.数据层.获取知识库详情(知识id)
            if 知识库详情:
                return {"success": True, "data": 知识库详情}
            else:
                return {"success": False, "error": f"知识库 {知识id} 不存在"}
        except Exception as e:
            知识库服务日志器.error(f"验证知识库存在失败: {str(e)}")
            return {"success": False, "error": f"验证知识库存在失败: {str(e)}"}

    async def _执行PostgreSQL向量检索(self, 查询文本: str, 知识id: int, 最大数量: int, 相似度阈值: float, 嵌入模型id: Optional[int] = None) -> List[Dict[str, Any]]:
        """执行PostgreSQL向量检索 - 使用原生asyncpg实现"""
        try:
            # 获取向量模型 - 优先使用知识库指定的嵌入模型
            from 服务.LangChain_模型管理器 import LangChain模型管理器实例

            向量模型 = None
            使用的模型id = 嵌入模型id

            # 如果没有指定嵌入模型，从知识库获取
            if not 使用的模型id:
                知识库详情 = await self.数据层.获取知识库详情(知识id)
                if 知识库详情:
                    使用的模型id = 知识库详情.get("嵌入模型")

            # 尝试获取指定的向量模型
            if 使用的模型id:
                向量模型 = await LangChain模型管理器实例.根据模型ID获取向量模型(使用的模型id)
                知识库服务日志器.info(f"使用知识库{知识id}的嵌入模型: ID={使用的模型id}")

            # 如果指定模型不可用，尝试获取默认向量模型
            if not 向量模型:
                知识库服务日志器.warning(f"指定向量模型(ID:{使用的模型id})不可用，尝试获取默认向量模型")
                默认模型结果 = await self.获取默认嵌入模型()
                if 默认模型结果.get("success"):
                    默认模型id = 默认模型结果["data"]["id"]
                    向量模型 = await LangChain模型管理器实例.根据模型ID获取向量模型(默认模型id)
                    使用的模型id = 默认模型id

            if not 向量模型:
                知识库服务日志器.error("无法获取任何可用的向量模型，跳过向量检索")
                return []

            # 使用原生asyncpg向量检索（避免langchain-postgres的psycopg依赖问题）
            # 生成查询向量
            查询向量 = await 向量模型.aembed_query(查询文本)
            向量字符串 = "[" + ",".join(map(str, 查询向量)) + "]"

            # 执行原生向量相似度搜索 - 使用参数化查询防止SQL注入
            检索SQL = """
            SELECT
                v.分块内容,
                v.分块序号,
                d.文档名称,
                d.文档uuid,
                v.元数据,
                d.id as 文档记录id,
                1 - (v.向量数据 <=> $1::vector) as 相似度分数
            FROM langchain_文档向量表 v
            JOIN langchain_知识库文档表 d ON v.langchain_知识库文档表id = d.id
            WHERE d.langchain_知识库表id = $2
            AND d.向量状态 = ANY($5::text[])
            AND v.向量数据 IS NOT NULL
            AND 1 - (v.向量数据 <=> $1::vector) >= $3
            ORDER BY v.向量数据 <=> $1::vector
            LIMIT $4
            """

            # 使用现有的数据库连接池执行查询 - 安全的参数化查询
            安全状态列表 = ['已完成', '基本完成']
            结果列表 = await self.数据层.数据库连接池.执行查询(
                检索SQL, (向量字符串, 知识id, 相似度阈值, 最大数量, 安全状态列表)
            )

            # 转换为标准格式
            检索结果 = []
            for 行 in 结果列表 or []:
                结果项 = {
                    "分块内容": 行.get("分块内容", ""),
                    "相似度分数": round(行.get("相似度分数", 0.0), 4),
                    "文档名称": 行.get("文档名称", ""),
                    "文档uuid": 行.get("文档uuid", ""),
                    "分块序号": 行.get("分块序号", 0),
                    "知识id": 知识id,
                    "文档记录id": 行.get("文档记录id", 0),
                    "分块大小": len(行.get("分块内容", "")),
                    "元数据": 行.get("元数据", {})
                }
                检索结果.append(结果项)

            知识库服务日志器.info(f"PostgreSQL向量检索完成: 知识id {知识id}, 查询: '{查询文本}', 返回: {len(检索结果)} 个结果")
            return 检索结果

        except Exception as e:
            知识库服务日志器.error(f"PostgreSQL向量检索失败: {str(e)}")
            return []

    def _清理元数据(self, 元数据: Any) -> Dict[str, Any]:
        """清理和标准化元数据"""
        try:
            if isinstance(元数据, dict):
                # 过滤掉已经在结果项中的字段
                排除字段 = {"相似度分数", "文档名称", "文档uuid", "分块序号"}
                return {k: v for k, v in 元数据.items() if k not in 排除字段}
            else:
                return {}
        except Exception:
            return {}

    async def _检查文档重复(self, 知识id: int, 文件MD5: str, 文件名: str) -> Dict[str, Any]:
        """检查文档MD5重复"""
        try:
            if not 文件MD5:
                return {"success": True}

            重复文档 = await self.数据层.检查文档MD5重复(知识id, 文件MD5)
            if 重复文档:
                知识库服务日志器.warning(
                    f"发现重复文档: {文件名}, MD5={文件MD5[:8]}..., "
                    f"已存在文档: {重复文档['文档名称']}"
                )
                return {
                    "success": False,
                    "error": f"该文件已存在，文档名称: {重复文档['文档名称']}",
                    "duplicate_info": {
                        "existing_document_id": 重复文档["id"],
                        "existing_document_name": 重复文档["文档名称"],
                        "existing_document_uuid": 重复文档["文档uuid"],
                        "file_md5": 文件MD5,
                    },
                }

            return {"success": True}

        except Exception as e:
            知识库服务日志器.error(f"检查文档重复失败: {str(e)}")
            return {"success": True}  # 检查失败时允许继续

    async def 智能体RAG检索(self, 智能体id: int, 用户输入: str) -> str:
        """为智能体提供简化的RAG检索服务 - 返回格式化的上下文文本"""
        try:
            # 获取智能体关联的知识库列表
            知识库列表 = await LangChain智能体数据层实例.获取智能体关联知识库列表(
                智能体id
            )
            if not 知识库列表:
                return ""

            知识库服务日志器.info(
                f"🔍 RAG检索开始: 查询='{用户输入[:50]}{'...' if len(用户输入) > 50 else ''}', 知识库数量={len(知识库列表)}"
            )

            # 从数据库获取RAG配置参数
            检索参数 = await self._获取智能体RAG配置(智能体id)

            # 执行检索
            检索结果 = await self.智能体检索测试(智能体id, 用户输入, 检索参数)

            if not 检索结果.get("success") or not 检索结果.get("data"):
                return ""

            # 提取检索到的文档片段
            文档片段 = 检索结果["data"].get("检索结果", [])
            if not 文档片段:
                return ""

            # 构建上下文文本
            上下文部分 = []
            for i, 片段 in enumerate(文档片段[:5], 1):  # 最多使用5个片段
                内容 = 片段.get("文档内容", "").strip()
                文档名称 = 片段.get("文档名称", "未知文档")
                相似度 = 片段.get("相似度分数", 0.0)

                if 内容 and len(内容) > 10:
                    片段标识 = f"[片段{i}]({文档名称}, 相似度:{相似度:.3f})"
                    上下文部分.append(f"{片段标识}\n{内容}")

            if not 上下文部分:
                return ""

            # 组合最终上下文
            最终上下文 = "\n\n".join(上下文部分)
            知识库服务日志器.info(
                f"✅ RAG检索完成: {len(上下文部分)}个片段, {len(最终上下文)}字符"
            )

            return 最终上下文

        except Exception as e:
            知识库服务日志器.error(f"❌ 智能体RAG检索失败: {str(e)}")
            return ""

    async def _获取智能体RAG配置(self, 智能体id: int) -> Dict[str, Any]:
        """获取智能体的RAG检索配置参数 - 从知识库关联表获取"""
        try:
            # 使用获取智能体关联知识库配置方法（返回完整配置信息）
            知识库关联列表 = await LangChain智能体数据层实例.获取智能体关联知识库配置(智能体id)

            if not 知识库关联列表:
                知识库服务日志器.warning(f"⚠️ 智能体 {智能体id} 未关联任何知识库")
                return {
                    "最大数量": 5,
                    "相似度阈值": 0.7,
                    "启用智能检索": True,
                    "检索策略": "similarity",
                    "知识库配置": []
                }

            # 处理知识库配置信息
            知识库配置列表 = []
            for 关联信息 in 知识库关联列表:
                try:
                    知识库配置 = {
                        "知识库id": 关联信息.get("知识id"),
                        "知识库名称": 关联信息.get("知识库名称"),
                        "嵌入模型id": 关联信息.get("嵌入模型id"),  # 从关联配置获取嵌入模型
                        "相似度阈值": 关联信息.get("相似度阈值", 0.7),
                        "最大检索数量": 关联信息.get("最大检索数量", 5),
                        "检索策略": 关联信息.get("检索策略", "similarity"),
                        "权重": 关联信息.get("权重", 1.0)
                    }
                    知识库配置列表.append(知识库配置)
                    知识库服务日志器.debug(f"成功获取知识库配置: {知识库配置}")
                except Exception as e:
                    知识库服务日志器.error(f"处理知识库关联信息失败: {str(e)}, 关联信息: {关联信息}")
                    continue

            if not 知识库配置列表:
                知识库服务日志器.warning(f"⚠️ 智能体 {智能体id} 的关联知识库配置为空")
                return {
                    "最大数量": 5,
                    "相似度阈值": 0.7,
                    "启用智能检索": True,
                    "检索策略": "similarity",
                    "知识库配置": []
                }

            # 使用第一个知识库的配置作为默认配置
            默认配置 = 知识库配置列表[0]

            检索参数 = {
                "最大数量": 默认配置["最大检索数量"],
                "相似度阈值": 默认配置["相似度阈值"],
                "启用智能检索": True,
                "检索策略": 默认配置["检索策略"],
                "知识库配置": 知识库配置列表  # 包含所有知识库的配置
            }

            知识库服务日志器.info(
                f"✅ 从知识库关联表获取RAG配置: 智能体{智能体id}, "
                f"关联{len(知识库配置列表)}个知识库, "
                f"默认配置: 最大数量={检索参数['最大数量']}, "
                f"相似度阈值={检索参数['相似度阈值']}, 检索策略={检索参数['检索策略']}"
            )

            return 检索参数

        except Exception as e:
            知识库服务日志器.error(f"❌ 获取智能体RAG配置失败: {str(e)}")
            # 返回默认配置
            return {
                "最大数量": 5,
                "相似度阈值": 0.7,
                "启用智能检索": True,
                "检索策略": "similarity",
                "知识库配置": []
            }




# 创建全局实例
LangChain知识库服务实例 = LangChain知识库服务()
