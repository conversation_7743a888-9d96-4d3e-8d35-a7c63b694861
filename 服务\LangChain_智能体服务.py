"""
LangChain智能体服务 - 架构优化后的核心业务服务
专注于复杂业务逻辑和状态管理

核心功能：
1. 智能体对话处理（涉及状态管理、统计更新）
2. 智能体生命周期管理（创建、更新、删除，涉及事务和缓存）
3. 工具配置管理（涉及缓存清理）
4. 系统管理和监控（依赖实例状态）

架构优化说明：
- 提取了纯函数和简单包装方法到LangChain_智能体工具函数.py
- 保留了需要状态管理、依赖注入、事务控制的复杂业务方法
- 专注于核心业务逻辑，提高代码内聚性
- 保持六大核心功能完整性
"""

import time
from datetime import datetime
from typing import Any, Dict, Optional, Union

# 数据层导入
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例

# 核心服务导入
from 服务.LangChain_复合智能体核心 import LangChain复合智能体管理器实例

# 工具函数导入 - 仅导入需要的函数
from 服务.LangChain_智能体工具函数 import (
    获取智能体工具配置,
    验证智能体存在
)

# 日志导入
from 日志 import 应用日志器 as 智能体服务日志器
from 状态 import 状态


class LangChain智能体服务:
    """LangChain智能体统一服务 - 重构后的简化版本

    采用纯委托模式，所有核心功能委托给专门的管理器处理
    """

    def __init__(self):
        """构造函数 - 最小化初始化"""
        self.已初始化: bool = True
        self.初始化时间: datetime = datetime.now()

        # 统计信息
        self.对话统计: Dict[str, Union[int, float]] = {
            "总对话数": 0,
            "成功对话数": 0,
            "失败对话数": 0,
            "平均响应时间": 0.0
        }

        智能体服务日志器.info("LangChain智能体服务创建成功")

    async def 智能体对话(
        self,
        智能体id: int,
        用户表id: int,
        用户消息: str,
        会话id: Optional[str] = None,
        测试模式: bool = False,
        自定义变量: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """智能体对话处理 - 委托给复合智能体核心"""
        开始时间 = time.time()
        
        try:
            智能体服务日志器.info(f"开始智能体对话: 智能体ID={智能体id}, 用户ID={用户表id}")
            
            # 生成会话ID
            if not 会话id:
                会话id = f"session_{智能体id}_{用户表id}_{int(time.time())}"

            # 获取或创建复合智能体实例
            复合智能体实例 = await LangChain复合智能体管理器实例.获取或创建实例(智能体id, 用户表id)
            
            # 处理对话
            回复内容 = await 复合智能体实例.处理对话(用户消息, 会话id, 自定义变量)
            
            # 更新统计信息
            耗时 = time.time() - 开始时间
            self._更新对话统计(True, 耗时)
            
            智能体服务日志器.info(f"🎯 对话完成: 回复{len(回复内容)}字符 | 总耗时={耗时:.2f}s")

            return {
                "status": 状态.通用.成功,
                "message": "智能体对话成功",
                "data": {
                    "智能体回复": 回复内容,
                    "智能体名称": 复合智能体实例.配置.智能体名称,
                    "会话id": 会话id,
                    "对话时间": str(int(datetime.now().timestamp())),
                    "处理时长": 耗时,
                    "令牌消耗": 复合智能体实例.最后对话令牌消耗,
                },
            }

        except Exception as e:
            耗时 = time.time() - 开始时间
            self._更新对话统计(False, 耗时)
            
            智能体服务日志器.error(f"智能体对话失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体更新失败,
                "message": f"智能体对话失败: {str(e)}",
                "data": None
            }

    def _更新对话统计(self, 成功: bool, 耗时: float):
        """更新对话统计信息"""
        try:
            self.对话统计["总对话数"] += 1
            
            if 成功:
                self.对话统计["成功对话数"] += 1
            else:
                self.对话统计["失败对话数"] += 1
            
            # 更新平均响应时间
            总耗时 = self.对话统计["平均响应时间"] * (self.对话统计["总对话数"] - 1) + 耗时
            self.对话统计["平均响应时间"] = 总耗时 / self.对话统计["总对话数"]
            
        except Exception as e:
            智能体服务日志器.error(f"更新对话统计失败: {str(e)}")

    async def 创建智能体(self, 智能体数据: Dict[str, Any]) -> Dict[str, Any]:
        """创建智能体 - 委托给数据层"""
        try:
            # 数据验证
            必需字段 = ["智能体名称", "模型名称", "用户表id"]
            for 字段 in 必需字段:
                if not 智能体数据.get(字段):
                    return {
                        "status": 状态.LangChain.参数错误,
                        "message": f"缺少必需字段: {字段}",
                        "data": None
                    }

            # 委托给数据层创建
            智能体id = await LangChain智能体数据层实例.创建智能体配置(智能体数据)
            
            if 智能体id:
                智能体服务日志器.info(f"✅ 智能体创建成功: ID={智能体id}")
                return {
                    "status": 状态.通用.成功,
                    "message": "智能体创建成功",
                    "data": {"智能体id": 智能体id}
                }
            else:
                return {
                    "status": 状态.LangChain.智能体创建失败,
                    "message": "智能体创建失败",
                    "data": None
                }

        except Exception as e:
            智能体服务日志器.error(f"创建智能体失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体创建失败,
                "message": f"创建智能体失败: {str(e)}",
                "data": None
            }

    async def 更新智能体(self, 智能体id: int, 更新数据: Dict[str, Any]) -> Dict[str, Any]:
        """更新智能体 - 委托给数据层"""
        try:
            # 委托给数据层更新
            成功 = await LangChain智能体数据层实例.更新智能体配置(智能体id, 更新数据)

            if 成功:
                # 清理缓存，确保下次使用新配置
                LangChain复合智能体管理器实例.清理实例缓存()

                # 获取更新后的智能体详情
                智能体详情 = await LangChain智能体数据层实例.获取智能体详情完整(智能体id)

                智能体服务日志器.info(f"✅ 智能体更新成功: ID={智能体id}")
                return {
                    "status": 状态.通用.成功,
                    "message": "智能体更新成功",
                    "data": {
                        "智能体详情": 智能体详情,
                        "更新字段": list(更新数据.keys()),
                        "更新时间": datetime.now().isoformat(),
                        "重载状态": "已清理缓存"
                    }
                }
            else:
                return {
                    "status": 状态.LangChain.智能体更新失败,
                    "message": "智能体更新失败",
                    "data": None
                }

        except Exception as e:
            智能体服务日志器.error(f"更新智能体失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体更新失败,
                "message": f"更新智能体失败: {str(e)}",
                "data": None
            }

    async def 删除智能体(self, 智能体id: int) -> Dict[str, Any]:
        """删除智能体 - 委托给数据层"""
        try:
            # 委托给数据层删除
            成功 = await LangChain智能体数据层实例.删除智能体配置(智能体id)
            
            if 成功:
                # 清理缓存
                LangChain复合智能体管理器实例.清理实例缓存()
                
                智能体服务日志器.info(f"✅ 智能体删除成功: ID={智能体id}")
                return {
                    "status": 状态.通用.成功,
                    "message": "智能体删除成功",
                    "data": None
                }
            else:
                return {
                    "status": 状态.LangChain.智能体更新失败,  # 使用更新失败代替删除失败
                    "message": "智能体删除失败",
                    "data": None
                }

        except Exception as e:
            智能体服务日志器.error(f"删除智能体失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体更新失败,
                "message": f"删除智能体失败: {str(e)}",
                "data": None
            }

    # 注意：获取智能体列表和获取智能体详情方法已移至LangChain_智能体工具函数.py
    # 路由应直接调用工具函数，而不是通过服务实例

    async def 获取服务统计(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            return {
                "status": 状态.通用.成功,
                "message": "获取服务统计成功",
                "data": {
                    "对话统计": self.对话统计,
                    "实例缓存数量": len(LangChain复合智能体管理器实例.实例缓存),
                    "服务运行时间": str(datetime.now() - self.初始化时间),
                    "初始化状态": self.已初始化
                }
            }

        except Exception as e:
            智能体服务日志器.error(f"获取服务统计失败: {str(e)}")
            return {
                "status": 状态.LangChain.配置错误,
                "message": f"获取服务统计失败: {str(e)}",
                "data": None
            }

    async def 清理缓存(self) -> Dict[str, Any]:
        """清理所有缓存"""
        try:
            LangChain复合智能体管理器实例.清理实例缓存()
            
            智能体服务日志器.info("✅ 缓存清理完成")
            return {
                "status": 状态.通用.成功,
                "message": "缓存清理完成",
                "data": None
            }

        except Exception as e:
            智能体服务日志器.error(f"清理缓存失败: {str(e)}")
            return {
                "status": 状态.LangChain.配置错误,
                "message": f"清理缓存失败: {str(e)}",
                "data": None
            }

    # 工具配置管理方法
    # 注意：获取智能体工具配置方法已移至LangChain_智能体工具函数.py

    async def 更新智能体工具配置(self, 智能体id: int, 工具配置: Dict[str, Any]) -> Dict[str, Any]:
        """更新智能体工具配置"""
        try:
            # 从工具配置中提取工具列表
            if isinstance(工具配置, dict) and "工具列表" in 工具配置:
                工具列表 = 工具配置["工具列表"]
            elif isinstance(工具配置, list):
                # 如果是工具配置字典列表，提取工具名称
                工具列表 = []
                for 工具 in 工具配置:
                    if isinstance(工具, dict):
                        工具名称 = 工具.get("工具名称")
                        if 工具名称:
                            工具列表.append(工具名称)
                    elif isinstance(工具, str):
                        工具列表.append(工具)
            else:
                工具列表 = []

            # 委托给数据层更新工具配置
            成功 = await LangChain智能体数据层实例.更新智能体工具关联(智能体id, 工具列表)

            if 成功:
                # 清理缓存，确保下次使用新配置
                LangChain复合智能体管理器实例.清理实例缓存()

                智能体服务日志器.info(f"✅ 智能体工具配置更新成功: ID={智能体id}")
                return {
                    "success": True,
                    "message": "智能体工具配置更新成功",
                    "data": {"智能体id": 智能体id}
                }
            else:
                return {
                    "success": False,
                    "error": "智能体工具配置更新失败"
                }
        except Exception as e:
            智能体服务日志器.error(f"更新智能体工具配置失败: {str(e)}")
            return {
                "success": False,
                "error": f"更新智能体工具配置失败: {str(e)}"
            }

    # 验证和权限相关方法
    async def 验证工具调用(self, 智能体id: int, 工具名称: str, 参数: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """验证智能体工具调用 - 复杂业务逻辑，保留在服务层"""
        try:
            # 验证智能体是否存在
            智能体详情 = await 验证智能体存在(智能体id)
            if not 智能体详情.get("success"):
                return {"success": False, "error": "智能体不存在"}

            # 获取智能体工具配置
            工具配置 = await 获取智能体工具配置(智能体id)
            if not 工具配置:
                return {"success": False, "error": "智能体未配置工具"}

            # 检查工具是否在配置中
            配置的工具列表 = []
            for 工具 in 工具配置:
                if isinstance(工具, dict):
                    工具名 = 工具.get("工具名称")
                    if 工具名:
                        配置的工具列表.append(工具名)
                elif isinstance(工具, str):
                    配置的工具列表.append(工具)
            if 工具名称 not in 配置的工具列表:
                return {"success": False, "error": f"工具 {工具名称} 未在智能体中配置"}

            return {
                "success": True,
                "data": {
                    "智能体id": 智能体id,
                    "工具名称": 工具名称,
                    "验证状态": "通过",
                    "配置的工具数量": len(配置的工具列表)
                }
            }

        except Exception as e:
            智能体服务日志器.error(f"验证工具调用失败: {str(e)}")
            return {"success": False, "error": f"验证工具调用失败: {str(e)}"}

    # 注意：验证智能体存在、验证用户智能体访问权限、获取用户可用智能体列表方法
    # 已移至LangChain_智能体工具函数.py

    # 移除冗余的包装方法，路由层应直接调用智能体对话方法

    # 系统管理相关方法
    async def 重新加载智能体(self) -> bool:
        """重新加载所有智能体配置"""
        try:
            # 清理复合智能体缓存
            LangChain复合智能体管理器实例.清理实例缓存()
            智能体服务日志器.info("✅ 智能体配置重新加载成功")
            return True
        except Exception as e:
            智能体服务日志器.error(f"重新加载智能体失败: {str(e)}")
            return False

    # 注意：获取使用统计数据、获取支持的文件格式方法已移至LangChain_智能体工具函数.py

    # 用户路由需要的方法 - 移除初始化方法，直接使用已初始化属性

    async def 获取用户对话历史(self, 用户id: int, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
        """获取用户对话历史"""
        try:
            # 委托给数据层获取对话历史
            对话历史, 总数量 = await LangChain智能体数据层实例.获取用户对话历史(
                用户id=用户id,
                智能体id=查询参数.get("智能体id"),
                页码=查询参数.get("页码", 1),
                每页数量=查询参数.get("每页数量", 20),
                开始时间=查询参数.get("开始时间"),
                结束时间=查询参数.get("结束时间")
            )

            return {
                "对话历史": 对话历史,
                "总数量": 总数量,
                "当前页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 20)
            }

        except Exception as e:
            智能体服务日志器.error(f"获取用户对话历史失败: {str(e)}")
            return {
                "对话历史": [],
                "总数量": 0,
                "当前页码": 1,
                "每页数量": 20
            }

    async def 清空智能体记忆(self, 用户id: int, 智能体id: int, 会话id: Optional[str] = None) -> Dict[str, Any]:
        """清空智能体记忆"""
        try:
            # 委托给数据层清空记忆
            成功 = await LangChain智能体数据层实例.清空智能体记忆(
                用户id=用户id,
                智能体id=智能体id,
                会话id=会话id
            )

            if 成功:
                # 同时清理复合智能体的缓存
                LangChain复合智能体管理器实例.清理实例缓存()

                智能体服务日志器.info(f"✅ 清空智能体记忆成功: 用户{用户id}, 智能体{智能体id}")
                return {
                    "success": True,
                    "message": "智能体记忆清空成功"
                }
            else:
                return {
                    "success": False,
                    "error": "清空智能体记忆失败"
                }

        except Exception as e:
            智能体服务日志器.error(f"清空智能体记忆失败: {str(e)}")
            return {
                "success": False,
                "error": f"清空智能体记忆失败: {str(e)}"
            }

    async def 获取用户智能体使用统计(self, 用户id: int) -> Dict[str, Any]:
        """获取用户智能体使用统计"""
        try:
            # 委托给数据层获取用户统计
            统计数据 = await LangChain智能体数据层实例.获取用户智能体使用统计(用户id)

            智能体服务日志器.info(f"✅ 获取用户智能体使用统计成功: 用户{用户id}")
            return {
                "success": True,
                "data": 统计数据
            }

        except Exception as e:
            智能体服务日志器.error(f"获取用户智能体使用统计失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取用户智能体使用统计失败: {str(e)}"
            }


# 创建全局实例
LangChain智能体服务实例 = LangChain智能体服务()
