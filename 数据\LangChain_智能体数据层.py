"""
LangChain智能体数据层

功能：
1. 智能体配置数据操作
2. 智能体状态管理
3. 智能体验证和查询
4. 用户智能体关联管理
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器 as 智能体数据日志器


class LangChain智能体数据层:
    """LangChain智能体配置数据层"""

    def __init__(self):
        self.已初始化: bool = False
        # 直接初始化数据库连接池实例 - 避免None类型问题
        self.数据库连接池 = 异步连接池实例

    async def 初始化(self) -> None:
        """初始化智能体数据层"""
        try:
            # 数据库连接池已在__init__中初始化，这里只需要标记为已初始化
            self.已初始化 = True
            智能体数据日志器.info("LangChain智能体数据层初始化成功")
        except Exception as e:
            智能体数据日志器.error(f"LangChain智能体数据层初始化失败: {str(e)}")
            raise

    # ==================== 对话记录相关操作 ====================

    async def 获取对话记录列表(
        self, 查询参数: Dict[str, Any]
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取对话记录列表"""
        try:
            # 构建查询条件
            查询条件 = []
            查询参数_值 = []
            参数索引 = 1

            if 查询参数.get("开始日期"):
                查询条件.append(f"lr.创建时间 >= ${参数索引}")
                查询参数_值.append(查询参数["开始日期"] + " 00:00:00")
                参数索引 += 1

            if 查询参数.get("结束日期"):
                查询条件.append(f"lr.创建时间 <= ${参数索引}")
                查询参数_值.append(查询参数["结束日期"] + " 23:59:59")
                参数索引 += 1

            if 查询参数.get("智能体id"):
                查询条件.append(f"lr.langchain_智能体配置表id = ${参数索引}")
                查询参数_值.append(查询参数["智能体id"])
                参数索引 += 1

            if 查询参数.get("用户id"):
                查询条件.append(f"lr.用户表id = ${参数索引}")
                查询参数_值.append(查询参数["用户id"])
                参数索引 += 1

            where_clause = "WHERE " + " AND ".join(查询条件) if 查询条件 else ""

            # 查询总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_对话记录表 lr
            {where_clause}
            """

            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数_值))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询详细记录
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 20)
            偏移量 = (页码 - 1) * 每页数量

            记录SQL = f"""
            SELECT
                lr.id,
                lr.用户表id,
                lr.langchain_智能体配置表id,
                lr.会话id,
                lr.用户消息,
                lr.智能体回复,
                lr.模型名称,
                lr.处理时长,
                lr.算力消耗,
                lr.令牌消耗,
                lr.创建时间,
                lc.智能体名称,
                COALESCE(NULLIF(u.昵称, ''), u.手机号) as 用户名称
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 lc ON lr.langchain_智能体配置表id = lc.id
            LEFT JOIN 用户表 u ON lr.用户表id = u.id
            {where_clause}
            ORDER BY lr.创建时间 DESC
            LIMIT ${len(查询参数_值) + 1} OFFSET ${len(查询参数_值) + 2}
            """

            查询参数_值.extend([每页数量, 偏移量])
            记录列表 = await self.数据库连接池.执行查询(记录SQL, tuple(查询参数_值))

            return 记录列表 if 记录列表 else [], 总数量

        except Exception as e:
            智能体数据日志器.error(f"获取对话记录列表失败: {str(e)}")
            return [], 0

    # ==================== 统计分析相关操作 ====================

    async def 获取基础统计数据(self, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
        """获取基础统计数据"""
        try:
            # 构建查询条件
            查询条件 = ""
            查询参数列表 = []

            if 查询参数.get("开始日期"):
                查询条件 += " AND lr.创建时间 >= $1"
                查询参数列表.append(查询参数["开始日期"] + " 00:00:00")

            if 查询参数.get("结束日期"):
                查询条件 += f" AND lr.创建时间 <= ${len(查询参数列表) + 1}"
                查询参数列表.append(查询参数["结束日期"] + " 23:59:59")

            统计SQL = f"""
            SELECT
                COUNT(*) as 总对话数,
                COUNT(DISTINCT lr.用户表id) as 活跃用户数,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 使用智能体数,
                AVG(lr.处理时长) as 平均处理时长,
                SUM(lr.算力消耗) as 总算力消耗,
                SUM(lr.令牌消耗) as 总令牌消耗
            FROM langchain_对话记录表 lr
            WHERE 1=1 {查询条件}
            """

            结果 = await self.数据库连接池.执行查询(统计SQL, tuple(查询参数列表))
            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体数据日志器.error(f"获取基础统计数据失败: {str(e)}")
            raise

    async def 获取智能体使用排行(
        self, 查询参数: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取智能体使用排行"""
        try:
            # 构建查询条件
            查询条件 = ""
            查询参数列表 = []

            if 查询参数.get("开始日期"):
                查询条件 += " AND lr.创建时间 >= $1"
                查询参数列表.append(查询参数["开始日期"] + " 00:00:00")

            if 查询参数.get("结束日期"):
                查询条件 += f" AND lr.创建时间 <= ${len(查询参数列表) + 1}"
                查询参数列表.append(查询参数["结束日期"] + " 23:59:59")

            排行SQL = f"""
            SELECT
                lc.id as 智能体id,
                lc.智能体名称,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.用户表id) as 用户数量,
                AVG(lr.处理时长) as 平均处理时长,
                SUM(lr.算力消耗) as 总算力消耗,
                SUM(lr.令牌消耗) as 总令牌消耗,
                MAX(lr.创建时间) as 最后使用时间
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 lc ON lr.langchain_智能体配置表id = lc.id
            WHERE 1=1 {查询条件}
            GROUP BY lc.id, lc.智能体名称
            ORDER BY 对话次数 DESC
            LIMIT 20
            """

            结果 = await self.数据库连接池.执行查询(排行SQL, tuple(查询参数列表))
            return list(结果) if 结果 else []

        except Exception as e:
            智能体数据日志器.error(f"获取智能体使用排行失败: {str(e)}")
            raise

    async def 获取用户活跃度统计(
        self, 查询参数: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取用户活跃度统计"""
        try:
            # 构建查询条件
            查询条件 = ""
            查询参数列表 = []

            if 查询参数.get("开始日期"):
                查询条件 += " AND lr.创建时间 >= $1"
                查询参数列表.append(查询参数["开始日期"] + " 00:00:00")

            if 查询参数.get("结束日期"):
                查询条件 += f" AND lr.创建时间 <= ${len(查询参数列表) + 1}"
                查询参数列表.append(查询参数["结束日期"] + " 23:59:59")

            活跃度SQL = f"""
            SELECT
                u.id as 用户id,
                COALESCE(NULLIF(u.昵称, ''), u.手机号) as 用户名,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 使用智能体数,
                SUM(lr.算力消耗) as 总算力消耗,
                SUM(lr.令牌消耗) as 总令牌消耗,
                MAX(lr.创建时间) as 最后活跃时间
            FROM langchain_对话记录表 lr
            LEFT JOIN 用户表 u ON lr.用户表id = u.id
            WHERE 1=1 {查询条件}
            GROUP BY u.id, COALESCE(NULLIF(u.昵称, ''), u.手机号)
            ORDER BY 对话次数 DESC
            LIMIT 20
            """

            结果 = await self.数据库连接池.执行查询(活跃度SQL, tuple(查询参数列表))
            return list(结果) if 结果 else []

        except Exception as e:
            智能体数据日志器.error(f"获取用户活跃度统计失败: {str(e)}")
            raise

    async def 获取今日统计(self) -> Dict[str, Any]:
        """获取今日统计"""
        try:
            今日SQL = """
            SELECT
                COUNT(*) as 今日对话数,
                COUNT(DISTINCT 用户表id) as 今日活跃用户,
                COUNT(DISTINCT langchain_智能体配置表id) as 今日使用智能体,
                SUM(算力消耗) as 今日算力消耗,
                SUM(令牌消耗) as 今日令牌消耗
            FROM langchain_对话记录表
            WHERE DATE(创建时间) = CURRENT_DATE
            """

            结果 = await self.数据库连接池.执行查询(今日SQL)
            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体数据日志器.error(f"获取今日统计失败: {str(e)}")
            raise

    async def 获取本周统计(self) -> Dict[str, Any]:
        """获取本周统计"""
        try:
            本周SQL = """
            SELECT
                COUNT(*) as 本周对话数,
                COUNT(DISTINCT 用户表id) as 本周活跃用户,
                COUNT(DISTINCT langchain_智能体配置表id) as 本周使用智能体,
                SUM(算力消耗) as 本周算力消耗,
                SUM(令牌消耗) as 本周令牌消耗
            FROM langchain_对话记录表
            WHERE EXTRACT(WEEK FROM 创建时间) = EXTRACT(WEEK FROM NOW())
            """

            结果 = await self.数据库连接池.执行查询(本周SQL)
            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体数据日志器.error(f"获取本周统计失败: {str(e)}")
            raise

    async def 获取本月统计(self) -> Dict[str, Any]:
        """获取本月统计"""
        try:
            本月SQL = """
            SELECT
                COUNT(*) as 本月对话数,
                COUNT(DISTINCT 用户表id) as 本月活跃用户,
                COUNT(DISTINCT langchain_智能体配置表id) as 本月使用智能体,
                SUM(算力消耗) as 本月算力消耗,
                SUM(令牌消耗) as 本月令牌消耗
            FROM langchain_对话记录表
            WHERE EXTRACT(YEAR FROM 创建时间) = EXTRACT(YEAR FROM NOW())
            AND EXTRACT(MONTH FROM 创建时间) = EXTRACT(MONTH FROM NOW())
            """

            结果 = await self.数据库连接池.执行查询(本月SQL)
            return 结果[0] if 结果 else {}

        except Exception as e:
            智能体数据日志器.error(f"获取本月统计失败: {str(e)}")
            raise

    async def 获取热门智能体(self) -> List[Dict[str, Any]]:
        """获取热门智能体（最近7天）"""
        try:
            热门SQL = """
            SELECT
                lc.id as 智能体id,
                lc.智能体名称,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.用户表id) as 用户数量
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 lc ON lr.langchain_智能体配置表id = lc.id
            WHERE lr.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            GROUP BY lc.id, lc.智能体名称
            ORDER BY 对话次数 DESC
            LIMIT 10
            """

            结果 = await self.数据库连接池.执行查询(热门SQL)
            return list(结果) if 结果 else []

        except Exception as e:
            智能体数据日志器.error(f"获取热门智能体失败: {str(e)}")
            raise

    async def 获取活跃用户(self) -> List[Dict[str, Any]]:
        """获取活跃用户（最近7天）"""
        try:
            活跃SQL = """
            SELECT
                u.id as 用户id,
                u.昵称,
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 使用智能体数
            FROM langchain_对话记录表 lr
            LEFT JOIN 用户表 u ON lr.用户表id = u.id
            WHERE lr.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            GROUP BY u.id, u.昵称
            ORDER BY 对话次数 DESC
            LIMIT 10
            """

            结果 = await self.数据库连接池.执行查询(活跃SQL)
            return list(结果) if 结果 else []

        except Exception as e:
            智能体数据日志器.error(f"获取活跃用户失败: {str(e)}")
            raise

    async def 获取智能体嵌入模型id(self, 智能体id: int) -> Optional[int]:
        """获取智能体关联的嵌入模型ID"""
        try:
            # 从智能体的知识库中获取嵌入模型
            查询SQL = """
            SELECT DISTINCT kb.嵌入模型
            FROM langchain_智能体配置表 lc
            JOIN langchain_智能体知识库关联表 rel ON lc.id = rel.langchain_智能体配置表id
            JOIN langchain_知识库表 kb ON rel.langchain_知识库表id = kb.id
            WHERE lc.id = $1
            LIMIT 1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (智能体id,))
            if 结果 and 结果[0].get("嵌入模型"):
                return 结果[0]["嵌入模型"]
            return None

        except Exception as e:
            智能体数据日志器.error(f"获取智能体嵌入模型ID失败: {str(e)}")
            return None

    async def 获取最近趋势(self) -> List[Dict[str, Any]]:
        """获取最近7天趋势"""
        try:
            趋势SQL = """
            SELECT
                DATE(创建时间) as 日期,
                COUNT(*) as 对话数量,
                COUNT(DISTINCT 用户表id) as 用户数量,
                COUNT(DISTINCT langchain_智能体配置表id) as 智能体数量
            FROM langchain_对话记录表
            WHERE 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            GROUP BY DATE(创建时间)
            ORDER BY 日期 ASC
            """

            结果 = await self.数据库连接池.执行查询(趋势SQL)
            return list(结果) if 结果 else []

        except Exception as e:
            智能体数据日志器.error(f"获取最近趋势失败: {str(e)}")
            raise

    async def 获取使用统计数据(
        self,
        开始日期: Optional[str] = None,
        结束日期: Optional[str] = None,
        智能体id: Optional[int] = None,
        用户id: Optional[int] = None,
        统计维度: str = "日",
        页码: int = 1,
        每页数量: int = 20
    ) -> Dict[str, Any]:
        """获取使用统计数据 - 支持多维度统计"""
        try:
            # 构建查询条件
            查询条件 = []
            查询参数 = []
            参数索引 = 1

            if 开始日期:
                查询条件.append(f"lr.创建时间 >= ${参数索引}")
                查询参数.append(开始日期 + " 00:00:00")
                参数索引 += 1

            if 结束日期:
                查询条件.append(f"lr.创建时间 <= ${参数索引}")
                查询参数.append(结束日期 + " 23:59:59")
                参数索引 += 1

            if 智能体id:
                查询条件.append(f"lr.langchain_智能体配置表id = ${参数索引}")
                查询参数.append(智能体id)
                参数索引 += 1

            if 用户id:
                查询条件.append(f"lr.用户表id = ${参数索引}")
                查询参数.append(用户id)
                参数索引 += 1

            条件字符串 = " AND ".join(查询条件) if 查询条件 else "1=1"

            # 根据统计维度确定分组字段
            if 统计维度 == "日":
                分组字段 = "DATE(lr.创建时间)"
                时间格式 = "DATE(lr.创建时间) as 统计日期"
            elif 统计维度 == "周":
                分组字段 = "EXTRACT(YEAR FROM lr.创建时间), EXTRACT(WEEK FROM lr.创建时间)"
                时间格式 = "EXTRACT(YEAR FROM lr.创建时间) || '-W' || EXTRACT(WEEK FROM lr.创建时间) as 统计周"
            elif 统计维度 == "月":
                分组字段 = "EXTRACT(YEAR FROM lr.创建时间), EXTRACT(MONTH FROM lr.创建时间)"
                时间格式 = "EXTRACT(YEAR FROM lr.创建时间) || '-' || LPAD(EXTRACT(MONTH FROM lr.创建时间)::text, 2, '0') as 统计月"
            else:
                分组字段 = "DATE(lr.创建时间)"
                时间格式 = "DATE(lr.创建时间) as 统计日期"

            # 计算偏移量
            偏移量 = (页码 - 1) * 每页数量

            # 获取统计数据
            统计SQL = f"""
            SELECT
                {时间格式},
                COUNT(*) as 对话次数,
                COUNT(DISTINCT lr.用户表id) as 用户数量,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 智能体数量,
                AVG(lr.处理时长) as 平均处理时长,
                SUM(lr.算力消耗) as 总算力消耗,
                SUM(lr.令牌消耗) as 总令牌消耗
            FROM langchain_对话记录表 lr
            WHERE {条件字符串}
            GROUP BY {分组字段}
            ORDER BY {分组字段} DESC
            LIMIT {每页数量} OFFSET {偏移量}
            """

            统计结果 = await self.数据库连接池.执行查询(统计SQL, tuple(查询参数))

            # 获取总数量
            计数SQL = f"""
            SELECT COUNT(DISTINCT {分组字段}) as 总数量
            FROM langchain_对话记录表 lr
            WHERE {条件字符串}
            """

            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数))
            总数量 = 计数结果[0]["总数量"] if 计数结果 else 0

            # 获取汇总信息
            汇总SQL = f"""
            SELECT
                COUNT(*) as 总调用次数,
                COUNT(DISTINCT lr.用户表id) as 总用户数,
                COUNT(DISTINCT lr.langchain_智能体配置表id) as 总智能体数,
                AVG(lr.处理时长) as 平均响应时间
            FROM langchain_对话记录表 lr
            WHERE {条件字符串}
            """

            汇总结果 = await self.数据库连接池.执行查询(汇总SQL, tuple(查询参数))
            汇总信息 = 汇总结果[0] if 汇总结果 else {}

            return {
                "统计数据": list(统计结果) if 统计结果 else [],
                "总数量": 总数量,
                "当前页码": 页码,
                "每页数量": 每页数量,
                "汇总信息": {
                    "总调用次数": 汇总信息.get("总调用次数", 0),
                    "总用户数": 汇总信息.get("总用户数", 0),
                    "总智能体数": 汇总信息.get("总智能体数", 0),
                    "平均响应时间": float(汇总信息.get("平均响应时间", 0.0) or 0.0)
                }
            }

        except Exception as e:
            智能体数据日志器.error(f"获取使用统计数据失败: {str(e)}")
            raise

    async def 获取智能体关联知识库列表(self, 智能体id: int) -> List[str]:
        """获取智能体关联的知识库ID列表"""
        try:
            查询SQL = """
            SELECT langchain_知识库表id
            FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1 AND 状态 = 'active'
            ORDER BY 权重 DESC, id ASC
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (智能体id,))
            知识id列表 = [str(行["langchain_知识库表id"]) for 行 in 结果 or []]

            智能体数据日志器.debug(
                f"智能体 {智能体id} 关联知识库: {len(知识id列表)} 个"
            )
            return 知识id列表

        except Exception as e:
            智能体数据日志器.error(f"获取智能体关联知识库列表失败: {str(e)}")
            return []

    async def 获取知识库嵌入模型(self, 知识id: int) -> Optional[int]:
        """获取知识库的嵌入模型ID"""
        try:
            查询SQL = "SELECT 嵌入模型 FROM langchain_知识库表 WHERE id = $1"
            结果 = await self.数据库连接池.执行查询(查询SQL, (知识id,))

            if 结果 and 结果[0].get("嵌入模型"):
                return 结果[0]["嵌入模型"]
            return None

        except Exception as e:
            智能体数据日志器.error(f"获取知识库嵌入模型失败: {str(e)}")
            return None

    # ==================== 智能体配置基础操作 ====================

    async def 获取智能体详情完整(self, 智能体id: int) -> Optional[Dict[str, Any]]:
        """获取智能体详情（包含提示词配置）"""
        try:
            详情SQL = """
            SELECT
                lc.id as 智能体id,
                lc.用户表id,
                lc.智能体名称,
                lc.智能体描述,
                lc.模型名称,
                lc.langchain_模型配置表id,
                lc.温度参数,
                lc.最大令牌数,
                lc.记忆窗口大小,
                lc.启用rag,
                lc.输出格式,
                lc.自定义回复格式,
                lc.自定义变量,
                lc.标签,
                lc.是否公开,
                lc.是否启用,
                lc.创建时间,
                lc.更新时间,
                lc.系统提示词, lc.用户提示词
            FROM langchain_智能体配置表 lc
            WHERE lc.id = $1
            """

            详情结果 = await self.数据库连接池.执行查询(详情SQL, (智能体id,))

            if not 详情结果:
                return None

            智能体详情 = 详情结果[0]

            # 处理JSON字段的解析
            json_字段 = [
                "自定义回复格式",
                "自定义变量",
                "标签",
            ]
            for 字段名 in json_字段:
                if 智能体详情.get(字段名):
                    if isinstance(智能体详情[字段名], str):
                        try:
                            智能体详情[字段名] = json.loads(智能体详情[字段名])
                        except json.JSONDecodeError:
                            智能体详情[字段名] = None

            智能体数据日志器.info(f"获取智能体详情成功: ID {智能体id}")
            return 智能体详情

        except Exception as e:
            智能体数据日志器.error(f"获取智能体详情失败: {str(e)}")
            raise

    async def 验证智能体存在(self, 智能体id: int) -> Optional[Dict[str, Any]]:
        """验证智能体是否存在并返回基本信息"""
        try:
            查询SQL = """
            SELECT id as 智能体id, 智能体名称, 智能体描述, 是否启用, 创建时间, 是否公开, 用户表id, 启用rag
            FROM langchain_智能体配置表
            WHERE id = $1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (智能体id,))

            if 结果:
                return 结果[0]
            return None

        except Exception as e:
            智能体数据日志器.error(f"验证智能体存在失败: {str(e)}")
            return None

    # 已删除重复方法 获取智能体详细信息，请使用 获取智能体详情完整 方法

    async def 创建智能体配置(self, 智能体数据: Dict[str, Any]) -> Optional[int]:
        """创建智能体配置"""
        try:
            插入SQL = """
            INSERT INTO langchain_智能体配置表 (
                用户表id, 智能体名称, 智能体描述, 模型名称,
                langchain_模型配置表id, 温度参数, 最大令牌数, 记忆窗口大小, 启用rag, 输出格式,
                自定义回复格式, 自定义变量, 标签, 是否公开, 是否启用, 创建时间, 更新时间
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW()) RETURNING id
            """

            # 处理自定义回复格式 - 直接等于JSON Schema，不包装
            自定义回复格式 = None

            # 优先检查直接传入的自定义回复格式
            if 智能体数据.get("自定义回复格式") is not None:
                自定义回复格式 = 智能体数据.get("自定义回复格式")
                智能体数据日志器.info(
                    f"✅ 使用直接传入的自定义回复格式: {type(自定义回复格式)}"
                )

            # 如果没有自定义回复格式，保持为None

            # RAG配置现在完全存储在知识库关联表中，不再使用主表的rag_配置字段

            # 处理自定义变量 - 专门的字段存储
            自定义变量 = 智能体数据.get("自定义变量", [])

            # 处理标签
            标签 = 智能体数据.get("标签", [])

            参数 = (
                智能体数据.get("用户表id"),
                智能体数据["智能体名称"],
                智能体数据.get("智能体描述", ""),
                智能体数据.get("模型名称", "qwen-turbo"),
                智能体数据.get("langchain_模型配置表id"),
                智能体数据.get("温度参数", 0.7),
                智能体数据.get("最大令牌数", 4000),
                智能体数据.get("记忆窗口大小", 10),
                智能体数据.get("启用rag", False),
                智能体数据.get("输出格式", "text"),
                json.dumps(自定义回复格式, ensure_ascii=False),
                json.dumps(自定义变量, ensure_ascii=False),
                json.dumps(标签, ensure_ascii=False),
                智能体数据.get("是否公开", True),
                智能体数据.get("是否启用", True),
            )

            # 执行插入并获取新创建的智能体id
            ID结果 = await self.数据库连接池.执行查询(插入SQL, 参数)
            智能体id = ID结果[0]["id"] if ID结果 else None

            # 工具关联现在通过单独的API管理，不在创建智能体时处理

            智能体数据日志器.info(
                f"智能体配置创建成功: {智能体数据['智能体名称']} (ID: {智能体id})"
            )
            return 智能体id

        except Exception as e:
            智能体数据日志器.error(f"创建智能体配置失败: {str(e)}")
            raise

    async def 更新智能体配置(self, 智能体id: int, 更新数据: Dict[str, Any]) -> bool:
        """更新智能体配置"""
        try:
            # 构建动态更新SQL
            更新字段 = []
            参数值 = []

            允许更新字段 = [
                "智能体名称",
                "智能体描述",
                "模型名称",
                "langchain_模型配置表id",
                "温度参数",
                "最大令牌数",
                "记忆窗口大小",
                "启用rag",
                "输出格式",
                "自定义回复格式",
                "自定义变量",
                "是否启用",
                "是否公开",
                "标签",
            ]

            # 处理自定义回复格式更新 - 直接等于JSON Schema
            参数索引 = 1
            if "自定义回复格式" in 更新数据:
                自定义回复格式 = 更新数据["自定义回复格式"]
                更新字段.append(f"自定义回复格式 = ${参数索引}")
                参数值.append(
                    json.dumps(自定义回复格式, ensure_ascii=False)
                    if 自定义回复格式
                    else None
                )
                参数索引 += 1
                智能体数据日志器.info(f"✅ 更新自定义回复格式: {type(自定义回复格式)}")

            # RAG配置现在存储在知识库关联表中，不在主表中存储
            # 如果有rag_配置更新，需要更新关联表
            if "rag_配置" in 更新数据:
                await self._更新智能体RAG配置到关联表(智能体id, 更新数据["rag_配置"])
                智能体数据日志器.info("✅ RAG配置已更新到知识库关联表")

            # 处理单独的RAG相关字段（向后兼容）
            rag_相关字段 = ["检索策略", "嵌入模型id", "相似度阈值", "最大检索数量", "查询优化配置"]
            if any(字段 in 更新数据 for 字段 in rag_相关字段):
                # 构建RAG配置对象
                rag_配置 = {}
                for 字段 in rag_相关字段:
                    if 字段 in 更新数据:
                        rag_配置[字段] = 更新数据[字段]

                # 更新到关联表
                await self._更新智能体RAG配置到关联表(智能体id, rag_配置)
                智能体数据日志器.info(f"✅ 单独RAG字段已更新到关联表: {list(rag_配置.keys())}")

            # 处理自定义变量更新 - 专门字段存储
            if "自定义变量" in 更新数据:
                自定义变量 = 更新数据.get("自定义变量", [])
                更新字段.append(f"自定义变量 = ${参数索引}")
                参数值.append(json.dumps(自定义变量, ensure_ascii=False))
                参数索引 += 1
                智能体数据日志器.info(f"✅ 更新自定义变量: {len(自定义变量)}个变量")

            # 知识库列表通过单独的关联表管理，不在主表中存储

            # 处理标签更新
            if "标签" in 更新数据:
                标签 = 更新数据.get("标签", [])
                更新字段.append(f"标签 = ${参数索引}")
                参数值.append(json.dumps(标签, ensure_ascii=False))
                参数索引 += 1
                智能体数据日志器.info(f"✅ 更新标签: {len(标签)}个标签")

            # 处理标准字段更新 - 排除已特殊处理的字段
            排除字段 = [
                "自定义回复格式",
                "输出模式",
                "pydantic_输出模式",
                "rag_配置",  # RAG配置现在通过关联表管理
                "检索策略",
                "嵌入模型id",
                "相似度阈值",
                "最大检索数量",
                "查询优化配置",
                "自定义变量",
                "知识库列表",
                "标签",
            ]
            for 字段名, 字段值 in 更新数据.items():
                if 字段名 in 允许更新字段 and 字段名 not in 排除字段:
                    更新字段.append(f"{字段名} = ${参数索引}")
                    参数值.append(字段值)
                    参数索引 += 1

            if not 更新字段:
                智能体数据日志器.warning(f"没有有效的更新字段: {智能体id}")
                return False

            # 添加更新时间
            更新字段.append("更新时间 = CURRENT_TIMESTAMP")
            参数值.append(智能体id)

            更新SQL = f"""
            UPDATE langchain_智能体配置表
            SET {", ".join(更新字段)}
            WHERE id = ${len(参数值)}
            """

            智能体数据日志器.info(f"执行更新SQL: {更新SQL}")
            智能体数据日志器.info(f"更新参数: {参数值}")

            await self.数据库连接池.执行更新(更新SQL, tuple(参数值))
            智能体数据日志器.info(f"智能体配置更新成功: {智能体id}")

            # 工具关联现在通过单独的API管理，不在更新智能体时处理

            return True

        except Exception as e:
            智能体数据日志器.error(f"更新智能体配置失败: {str(e)}")
            raise

    async def _更新智能体RAG配置到关联表(self, 智能体id: int, rag_配置: Dict[str, Any]):
        """将RAG配置更新到知识库关联表中"""
        try:
            if not isinstance(rag_配置, dict):
                智能体数据日志器.warning(f"RAG配置不是字典格式: {type(rag_配置)}")
                return

            # 获取该智能体的所有知识库关联
            查询关联SQL = """
            SELECT id, langchain_知识库表id
            FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1
            """
            关联记录 = await self.数据库连接池.执行查询(查询关联SQL, (智能体id,))

            if not 关联记录:
                智能体数据日志器.info(f"智能体 {智能体id} 没有关联的知识库，跳过RAG配置更新")
                return

            # 更新每个关联记录的RAG配置
            for 记录 in 关联记录:
                关联id = 记录["id"]

                # 构建更新字段
                更新字段 = []
                参数值 = []
                参数索引 = 1

                # 映射RAG配置字段到关联表字段
                字段映射 = {
                    "检索策略": "检索策略",
                    "相似度阈值": "相似度阈值",
                    "最大检索数量": "最大检索数量"
                }

                for rag_字段, 关联表字段 in 字段映射.items():
                    if rag_字段 in rag_配置:
                        更新字段.append(f"{关联表字段} = ${参数索引}")
                        参数值.append(rag_配置[rag_字段])
                        参数索引 += 1

                # 处理查询优化配置
                if "查询优化配置" in rag_配置:
                    查询优化配置 = rag_配置["查询优化配置"]
                    if isinstance(查询优化配置, dict):
                        # 分别更新查询优化相关字段
                        if "启用查询优化" in 查询优化配置:
                            更新字段.append(f"启用查询优化 = ${参数索引}")
                            参数值.append(1 if 查询优化配置["启用查询优化"] else 0)
                            参数索引 += 1

                        if "查询优化策略" in 查询优化配置:
                            更新字段.append(f"查询优化策略 = ${参数索引}")
                            参数值.append(查询优化配置["查询优化策略"])
                            参数索引 += 1

                        if "查询优化模型id" in 查询优化配置:
                            更新字段.append(f"查询优化模型id = ${参数索引}")
                            参数值.append(查询优化配置["查询优化模型id"])
                            参数索引 += 1

                        if "查询优化提示词" in 查询优化配置:
                            更新字段.append(f"查询优化提示词 = ${参数索引}")
                            参数值.append(查询优化配置["查询优化提示词"])
                            参数索引 += 1

                # 执行更新
                if 更新字段:
                    更新SQL = f"""
                    UPDATE langchain_智能体知识库关联表
                    SET {", ".join(更新字段)}
                    WHERE id = ${参数索引}
                    """
                    参数值.append(关联id)

                    await self.数据库连接池.执行更新(更新SQL, tuple(参数值))
                    智能体数据日志器.info(f"✅ 更新关联表RAG配置成功: 关联ID={关联id}")

        except Exception as e:
            智能体数据日志器.error(f"更新RAG配置到关联表失败: {str(e)}")
            raise

    async def 更新智能体配置完整(self, 智能体id: int, 更新数据: Dict[str, Any]) -> bool:
        """更新智能体配置（包括提示词）"""
        try:
            # 验证智能体是否存在
            验证SQL = "SELECT id, 智能体名称 FROM langchain_智能体配置表 WHERE id = $1"
            智能体结果 = await self.数据库连接池.执行查询(验证SQL, (智能体id,))

            if not 智能体结果:
                return False

            # 更新智能体基本配置
            更新字段 = []
            更新参数 = []

            基本字段映射 = {
                "智能体名称": "智能体名称",
                "智能体描述": "智能体描述",
                "模型名称": "模型名称",
                "langchain_模型配置表id": "langchain_模型配置表id",
                "温度参数": "温度参数",
                "最大令牌数": "最大令牌数",
                "记忆窗口大小": "记忆窗口大小",
                "启用rag": "启用rag",
                "输出格式": "输出格式",
                "是否启用": "是否启用",
            }

            参数索引 = 1
            for 字段名, 数据库字段 in 基本字段映射.items():
                if 字段名 in 更新数据 and 更新数据[字段名] is not None:
                    更新字段.append(f"{数据库字段} = ${参数索引}")
                    更新参数.append(更新数据[字段名])
                    参数索引 += 1

            # 处理基本配置字段（是否公开、标签等）
            if "是否公开" in 更新数据:
                更新字段.append(f"是否公开 = ${参数索引}")
                更新参数.append(更新数据["是否公开"])
                参数索引 += 1

            if "标签" in 更新数据:
                更新字段.append(f"标签 = ${参数索引}")
                更新参数.append(json.dumps(更新数据["标签"], ensure_ascii=False))
                参数索引 += 1

            # 执行基本配置更新
            if 更新字段:
                更新字段.append("更新时间 = CURRENT_TIMESTAMP")
                更新SQL = f"""
                UPDATE langchain_智能体配置表
                SET {", ".join(更新字段)}
                WHERE id = $1
                """
                更新参数.append(智能体id)
                await self.数据库连接池.执行更新(更新SQL, tuple(更新参数))

            # 提示词字段现在直接在智能体配置表中，无需单独更新

            智能体数据日志器.info(f"更新智能体配置成功: ID {智能体id}")
            return True

        except Exception as e:
            智能体数据日志器.error(f"更新智能体配置失败: {str(e)}")
            raise

    # 提示词配置现在直接在智能体配置表中，无需单独的创建方法

    # 提示词配置现在直接在智能体配置表中，无需单独的更新方法

    # 提示词更新通知已移除，因为提示词现在直接在智能体配置表中

    async def 删除智能体配置(self, 智能体id: int) -> bool:
        """删除智能体配置"""
        try:
            删除SQL = "DELETE FROM langchain_智能体配置表 WHERE id = $1"
            await self.数据库连接池.执行更新(删除SQL, (智能体id,))
            智能体数据日志器.info(f"智能体配置删除成功: {智能体id}")
            return True

        except Exception as e:
            智能体数据日志器.error(f"删除智能体配置失败: {str(e)}")
            raise

    async def 获取智能体列表(
        self,
        页码: int = 1,
        每页数量: int = 20,
        搜索关键词: Optional[str] = None,
        状态: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取智能体列表"""
        try:
            # 构建查询条件
            查询条件 = []
            参数值 = []
            参数索引 = 1

            if 搜索关键词:
                查询条件.append(
                    f"(智能体名称 LIKE ${参数索引} OR 智能体描述 LIKE ${参数索引 + 1})"
                )
                关键词 = f"%{搜索关键词}%"
                参数值.extend([关键词, 关键词])
                参数索引 += 2

            # 智能体类型字段已移除

            if 状态:
                查询条件.append(f"状态 = ${参数索引}")
                参数值.append(状态)
                参数索引 += 1

            where_clause = f"WHERE {' AND '.join(查询条件)}" if 查询条件 else ""

            # 获取总数
            计数SQL = (
                f"SELECT COUNT(*) as total FROM langchain_智能体配置表 {where_clause}"
            )
            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(参数值))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 获取数据
            偏移量 = (页码 - 1) * 每页数量
            数据SQL = f"""
            SELECT * FROM langchain_智能体配置表
            {where_clause}
            ORDER BY 创建时间 DESC
            LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """
            参数值.extend([每页数量, 偏移量])

            数据结果 = await self.数据库连接池.执行查询(数据SQL, tuple(参数值))

            return list(数据结果) if 数据结果 else [], 总数量

        except Exception as e:
            智能体数据日志器.error(f"获取智能体配置列表失败: {str(e)}")
            raise

    # ==================== 用户智能体关联管理 ====================

    async def 创建用户智能体关联(
        self, 用户id: int, 智能体id: int, 分配数据: Dict[str, Any]
    ) -> bool:
        """创建用户智能体关联"""
        try:
            插入SQL = """
            INSERT INTO langchain_用户智能体关联表
            (用户表id, langchain_智能体配置表id, 分配类型, 备注, 状态)
            VALUES ($1, $2, $3, $4, $5)
            """

            参数 = (
                用户id,
                智能体id,
                分配数据.get("分配类型", "手动分配"),
                分配数据.get("备注", ""),
                分配数据.get("状态", "启用"),
            )

            影响行数 = await self.数据库连接池.执行更新(插入SQL, 参数)
            return 影响行数 > 0

        except Exception as e:
            智能体数据日志器.error(f"创建用户智能体关联失败: {str(e)}")
            raise

    async def 获取用户智能体关联列表(
        self, 用户id: Optional[int] = None, 智能体id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """获取用户智能体关联列表"""
        try:
            查询条件 = []
            参数值 = []
            参数索引 = 1

            if 用户id:
                查询条件.append("ua.用户表id = $1")
                参数值.append(用户id)
                参数索引 += 1

            if 智能体id:
                查询条件.append("ua.langchain_智能体配置表id = $1")
                参数值.append(智能体id)
                参数索引 += 1

            where_clause = f"WHERE {' AND '.join(查询条件)}" if 查询条件 else ""

            查询SQL = f"""
            SELECT
                ua.用户表id, ua.langchain_智能体配置表id, ua.分配类型, ua.备注, ua.状态, ua.创建时间,
                lc.智能体名称, lc.智能体描述
            FROM langchain_用户智能体关联表 ua
            LEFT JOIN langchain_智能体配置表 lc ON ua.langchain_智能体配置表id = lc.id
            {where_clause}
            ORDER BY ua.创建时间 DESC
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, tuple(参数值))
            return list(结果) if 结果 else []

        except Exception as e:
            智能体数据日志器.error(f"获取用户智能体关联列表失败: {str(e)}")
            raise

    async def 删除用户智能体关联(self, 用户id: int, 智能体id: int) -> bool:
        """删除用户智能体关联"""
        try:
            删除SQL = "DELETE FROM langchain_用户智能体关联表 WHERE 用户表id = $1 AND langchain_智能体配置表id = $2"
            await self.数据库连接池.执行更新(删除SQL, (用户id, 智能体id))
            智能体数据日志器.info(
                f"用户智能体关联删除成功: 用户{用户id} -> 智能体{智能体id}"
            )
            return True

        except Exception as e:
            智能体数据日志器.error(f"删除用户智能体关联失败: {str(e)}")
            raise

    # ==================== 对话记录管理 ====================

    async def 创建对话记录(self, 对话数据: Dict[str, Any]) -> bool:
        """创建对话记录"""
        try:
            插入SQL = """
            INSERT INTO langchain_对话记录表
            (用户表id, langchain_智能体配置表id, 会话id, 用户消息, 智能体回复,
             模型名称, 处理时长, 算力消耗, 令牌消耗, 调用来源,
             实际输入token, 实际输出token, 实际token总数, 估算输入token, 估算输出token, 估算token总数, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW())
            """

            参数 = (
                对话数据.get("用户表id"),
                对话数据.get("langchain_智能体配置表id"),
                对话数据.get("会话id"),
                对话数据.get("用户消息", ""),
                对话数据.get("智能体回复", ""),
                对话数据.get("模型名称", ""),
                对话数据.get("处理时长", 0),
                对话数据.get("算力消耗", 0),
                对话数据.get("令牌消耗", 0),
                对话数据.get("调用来源", "用户"),
                对话数据.get("实际输入token", 0),
                对话数据.get("实际输出token", 0),
                对话数据.get("实际token总数", 0),
                对话数据.get("估算输入token", 0),
                对话数据.get("估算输出token", 0),
                对话数据.get("估算token总数", 0),
            )

            await self.数据库连接池.执行插入(插入SQL, 参数)
            智能体数据日志器.info(
                f"对话记录创建成功: 用户{对话数据.get('用户表id')} -> 智能体{对话数据.get('langchain_智能体配置表id')}"
            )
            return True

        except Exception as e:
            智能体数据日志器.error(f"创建对话记录失败: {str(e)}")
            raise

    async def 获取智能体关联知识库(self, 智能体id: int) -> List[Dict[str, Any]]:
        """获取智能体关联的知识库详细信息列表 - 使用关联表"""
        try:
            # 通过关联表获取知识id列表
            关联查询SQL = """
            SELECT langchain_知识库表id
            FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1 AND 状态 = 'active'
            ORDER BY 权重 DESC, id ASC
            """

            关联结果 = await self.数据库连接池.执行查询(关联查询SQL, (智能体id,))

            if not 关联结果:
                return []

            # 获取每个知识库的详细信息
            知识库详情列表 = []
            for 关联记录 in 关联结果:
                try:
                    知识id = 关联记录["langchain_知识库表id"]
                    详情SQL = """
                    SELECT id as 知识id, 知识库名称, 知识库描述, 知识库状态, 文档数量
                    FROM langchain_知识库表
                    WHERE id = $1 AND 知识库状态 != '已删除'
                    """
                    详情结果 = await self.数据库连接池.执行查询(详情SQL, (知识id,))

                    if 详情结果:
                        知识库详情列表.append(详情结果[0])

                except (ValueError, TypeError) as e:
                    智能体数据日志器.warning(f"无效的知识id: {知识id}, 错误: {str(e)}")
                    continue

            智能体数据日志器.info(
                f"获取智能体 {智能体id} 关联知识库成功: {len(知识库详情列表)} 个知识库"
            )
            return 知识库详情列表

        except Exception as e:
            智能体数据日志器.error(f"获取智能体关联知识库失败: {str(e)}")
            return []

    async def 获取智能体关联知识库配置(self, 智能体id: int) -> List[Dict[str, Any]]:
        """获取智能体关联知识库的完整配置信息，包括查询优化配置"""
        try:
            # 通过关联表获取完整配置信息
            关联查询SQL = """
            SELECT
                akr.id as 关联id,
                akr.langchain_知识库表id as 知识id,
                akr.权重,
                akr.检索策略,
                akr.最大检索数量,
                akr.相似度阈值,
                akr.状态 as 关联状态,
                akr.启用查询优化,
                akr.查询优化策略,
                akr.查询优化模型id,
                akr.查询优化提示词,
                kb.知识库名称,
                kb.知识库描述,
                kb.知识库状态,
                kb.嵌入模型 as 嵌入模型id
            FROM langchain_智能体知识库关联表 akr
            JOIN langchain_知识库表 kb ON akr.langchain_知识库表id = kb.id
            WHERE akr.langchain_智能体配置表id = $1 AND akr.状态 = 'active'
            AND kb.知识库状态 != '已删除'
            ORDER BY akr.权重 DESC, akr.id ASC
            """

            关联结果 = await self.数据库连接池.执行查询(关联查询SQL, (智能体id,))

            if not 关联结果:
                return []

            # 处理每个关联配置
            配置列表 = []
            for 关联记录 in 关联结果:
                # 构建查询优化配置
                查询优化配置 = {}
                if 关联记录.get("启用查询优化"):
                    查询优化配置 = {
                        "启用": bool(关联记录.get("启用查询优化", False)),
                        "优化策略": 关联记录.get("查询优化策略", "rewrite"),
                        "优化模型id": 关联记录.get("查询优化模型id"),
                        "提示词模板": 关联记录.get("查询优化提示词", ""),
                    }

                # 构建完整配置
                配置信息 = {
                    "关联id": 关联记录["关联id"],
                    "知识id": 关联记录["知识id"],
                    "知识库名称": 关联记录["知识库名称"],
                    "知识库描述": 关联记录["知识库描述"],
                    "知识库状态": 关联记录["知识库状态"],
                    "权重": 关联记录["权重"],
                    "检索策略": 关联记录["检索策略"],
                    "最大检索数量": 关联记录["最大检索数量"],
                    "相似度阈值": 关联记录["相似度阈值"],
                    "关联状态": 关联记录["关联状态"],
                    "嵌入模型id": 关联记录["嵌入模型id"],
                    "查询优化配置": 查询优化配置,
                }

                配置列表.append(配置信息)

            智能体数据日志器.info(
                f"获取智能体 {智能体id} 关联知识库配置成功: {len(配置列表)} 个知识库"
            )
            return 配置列表

        except Exception as e:
            智能体数据日志器.error(f"获取智能体关联知识库配置失败: {str(e)}")
            return []

    # ==================== 智能体统计和监控 ====================

    async def 获取智能体统计数据(self) -> Dict[str, Any]:
        """获取智能体统计数据"""
        try:
            # 获取智能体统计
            智能体统计SQL = """
            SELECT
                COUNT(*) as 总数量,
                COUNT(CASE WHEN 是否启用 = true THEN 1 END) as 启用数量,
                COUNT(CASE WHEN 是否启用 = false THEN 1 END) as 禁用数量,
                COUNT(CASE WHEN 启用rag = 1 THEN 1 END) as RAG智能体数量,
                COUNT(CASE WHEN 是否公开 = 1 THEN 1 END) as 公开智能体数量,
                COUNT(CASE WHEN 是否公开 = 0 THEN 1 END) as 私有智能体数量
            FROM langchain_智能体配置表
            """

            智能体结果 = await self.数据库连接池.执行查询(智能体统计SQL)
            智能体统计 = 智能体结果[0] if 智能体结果 else {}

            # 获取用户分配统计
            分配统计SQL = """
            SELECT
                COUNT(*) as 总分配数量,
                COUNT(DISTINCT 用户表id) as 分配用户数量,
                COUNT(DISTINCT langchain_智能体配置表id) as 被分配智能体数量
            FROM langchain_用户智能体关联表
            WHERE 状态 = '启用'
            """

            分配结果 = await self.数据库连接池.执行查询(分配统计SQL)
            分配统计 = 分配结果[0] if 分配结果 else {}

            # 获取对话统计
            对话统计SQL = """
            SELECT
                COUNT(*) as 总对话数量,
                COUNT(DISTINCT 用户表id) as 活跃用户数量,
                COUNT(DISTINCT langchain_智能体配置表id) as 活跃智能体数量,
                AVG(处理时长) as 平均处理时长
            FROM langchain_对话记录表
            WHERE 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
            """

            对话结果 = await self.数据库连接池.执行查询(对话统计SQL)
            对话统计 = 对话结果[0] if 对话结果 else {}

            return {
                "智能体统计": 智能体统计,
                "分配统计": 分配统计,
                "对话统计": 对话统计,
                "统计时间": datetime.now().isoformat(),
            }

        except Exception as e:
            智能体数据日志器.error(f"获取智能体统计数据失败: {str(e)}")
            raise

    async def 获取智能体列表详细(
        self,
        筛选条件: Optional[Dict[str, Any]] = None,
        分页参数: Optional[Dict[str, Any]] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取智能体列表详细信息（管理端）"""
        try:
            # 构建查询条件
            查询条件 = []
            查询参数 = []
            参数索引 = 1

            if 筛选条件:
                # 修复：只有当智能体名称不为空时才添加搜索条件
                智能体名称 = 筛选条件.get("智能体名称")
                if 智能体名称 and 智能体名称.strip():
                    查询条件.append(f"lc.智能体名称 LIKE ${参数索引}")
                    查询参数.append(f"%{智能体名称.strip()}%")
                    参数索引 += 1

                # 修复：只有当搜索关键词不为空时才添加搜索条件
                搜索关键词 = 筛选条件.get("搜索关键词")
                if 搜索关键词 and 搜索关键词.strip():
                    查询条件.append(
                        f"(lc.智能体名称 LIKE ${参数索引} OR lc.智能体描述 LIKE ${参数索引 + 1})"
                    )
                    关键词 = f"%{搜索关键词.strip()}%"
                    查询参数.extend([关键词, 关键词])
                    参数索引 += 2

                # 智能体类型筛选已移除

                # 状态筛选已改为是否启用筛选，在前面已处理

                if 筛选条件.get("是否公开") is not None:
                    # 处理是否公开筛选条件，支持布尔值和数字值
                    是否公开值 = 筛选条件["是否公开"]
                    if isinstance(是否公开值, bool):
                        是否公开值 = 1 if 是否公开值 else 0
                    查询条件.append(f"lc.是否公开 = ${参数索引}")
                    查询参数.append(是否公开值)
                    参数索引 += 1

                if 筛选条件.get("创建时间范围"):
                    时间范围 = 筛选条件["创建时间范围"]
                    # {{ AURA-X: Modify - 修复PostgreSQL参数占位符语法错误. Approval: 寸止(ID:1735372800). }}
                    # {{ Source: context7-mcp on 'PostgreSQL Parameter Syntax' }}
                    if 时间范围.get("开始时间"):
                        查询条件.append(f"lc.创建时间 >= ${参数索引}")
                        查询参数.append(时间范围["开始时间"])
                        参数索引 += 1
                    if 时间范围.get("结束时间"):
                        查询条件.append(f"lc.创建时间 <= ${参数索引}")
                        查询参数.append(时间范围["结束时间"])
                        参数索引 += 1

            where_clause = f"WHERE {' AND '.join(查询条件)}" if 查询条件 else ""

            # 获取总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_智能体配置表 lc
            {where_clause}
            """
            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 获取分页参数
            页码 = 分页参数.get("页码", 1) if 分页参数 else 1
            每页数量 = 分页参数.get("每页数量", 10) if 分页参数 else 10
            偏移量 = (页码 - 1) * 每页数量

            # 获取详细数据 - 修复参数索引问题
            详细SQL = f"""
            SELECT
                lc.id,
                lc.用户表id,
                lc.智能体名称,
                lc.智能体描述,
                lc.模型名称,
                lc.温度参数,
                lc.最大令牌数,
                lc.记忆窗口大小,
                lc.启用rag,
                lc.输出格式,
                lc.自定义回复格式,
                lc.是否公开,
                lc.是否启用,
                lc.创建时间,
                lc.更新时间,
                (SELECT COUNT(*) FROM langchain_用户智能体关联表 ua WHERE ua.langchain_智能体配置表id = lc.id AND ua.状态 = '启用') as 分配用户数量,
                (SELECT COUNT(*) FROM langchain_对话记录表 cr WHERE cr.langchain_智能体配置表id = lc.id) as 对话次数,
                (SELECT MAX(cr.创建时间) FROM langchain_对话记录表 cr WHERE cr.langchain_智能体配置表id = lc.id) as 最后对话时间
            FROM langchain_智能体配置表 lc
            {where_clause}
            ORDER BY lc.创建时间 DESC
            LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """

            查询参数.extend([每页数量, 偏移量])
            详细结果 = await self.数据库连接池.执行查询(详细SQL, tuple(查询参数))

            return list(详细结果) if 详细结果 else [], 总数量

        except Exception as e:
            智能体数据日志器.error(f"获取智能体列表详细失败: {str(e)}")
            raise

    async def 删除智能体配置完整(self, 智能体id: int) -> bool:
        """完整删除智能体配置（包括关联数据）"""
        try:
            # 删除用户智能体关联
            await self.数据库连接池.执行更新(
                "DELETE FROM langchain_用户智能体关联表 WHERE langchain_智能体配置表id = $1",
                (智能体id,),
            )

            # 删除对话记录
            await self.数据库连接池.执行更新(
                "DELETE FROM langchain_对话记录表 WHERE langchain_智能体配置表id = $1",
                (智能体id,),
            )

            # 删除知识库关联
            await self.数据库连接池.执行更新(
                "DELETE FROM langchain_智能体知识库关联表 WHERE langchain_智能体配置表id = $1",
                (智能体id,),
            )

            # 提示词配置现在直接在智能体配置表中，无需单独删除

            # 删除智能体配置
            await self.数据库连接池.执行更新(
                "DELETE FROM langchain_智能体配置表 WHERE id = $1", (智能体id,)
            )

            智能体数据日志器.info(f"智能体配置完整删除成功: ID {智能体id}")
            return True

        except Exception as e:
            智能体数据日志器.error(f"删除智能体配置完整失败: {str(e)}")
            raise

    async def 获取用户智能体分配列表详细(
        self,
        用户id: Optional[int] = None,
        智能体id: Optional[int] = None,
        分页参数: Optional[Dict[str, Any]] = None,
        只显示启用: bool = True,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户智能体分配列表（详细信息）"""
        try:
            # 构建查询条件
            查询条件 = []
            查询参数 = []
            参数索引 = 1

            if 只显示启用:
                查询条件.append("ua.状态 = '启用'")  # 只显示启用状态的分配

            if 用户id:
                查询条件.append(f"ua.用户表id = ${参数索引}")
                查询参数.append(用户id)
                参数索引 += 1

            if 智能体id:
                查询条件.append(f"ua.langchain_智能体配置表id = ${参数索引}")
                查询参数.append(智能体id)
                参数索引 += 1

            where_clause = f"WHERE {' AND '.join(查询条件)}" if 查询条件 else ""

            # 获取总数
            计数SQL = f"""
            SELECT COUNT(*) as total
            FROM langchain_用户智能体关联表 ua
            {where_clause}
            """
            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 获取分页参数
            页码 = 分页参数.get("页码", 1) if 分页参数 else 1
            每页数量 = 分页参数.get("每页数量", 10) if 分页参数 else 10
            偏移量 = (页码 - 1) * 每页数量

            # 获取详细数据
            详细SQL = f"""
            SELECT
                ua.*,
                u.昵称, u.邮箱, u.手机号,
                lc.智能体名称, lc.智能体描述, lc.是否启用 as 智能体启用状态,
                0 as 对话次数,
                NULL as 最后对话时间
            FROM langchain_用户智能体关联表 ua
            LEFT JOIN 用户表 u ON ua.用户表id = u.id
            LEFT JOIN langchain_智能体配置表 lc ON ua.langchain_智能体配置表id = lc.id
            {where_clause}
            ORDER BY ua.创建时间 DESC
            LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """

            查询参数.extend([每页数量, 偏移量])
            详细结果 = await self.数据库连接池.执行查询(详细SQL, tuple(查询参数))

            return list(详细结果) if 详细结果 else [], 总数量

        except Exception as e:
            智能体数据日志器.error(f"获取用户智能体分配列表详细失败: {str(e)}")
            raise

    async def 验证用户智能体权限(self, 用户id: int, 智能体id: int) -> bool:
        """
        验证用户是否有权限使用指定智能体

        权限规则：
        1. 如果智能体是否公开=0（私有）：通过智能体配置表中的用户表id判断归属
        2. 如果智能体是否公开=1（公开）：通过用户智能体关联表来判断归属
        """
        try:
            # 检查用户权限的完整逻辑
            验证SQL = """
            SELECT COUNT(*) as count FROM (
                -- 1. 检查私有智能体的创建者权限
                SELECT 1 FROM langchain_智能体配置表 lc
                WHERE lc.id = $1
                AND lc.用户表id = $2
                AND lc.是否公开 = 0
                AND lc.是否启用 = true

                UNION

                -- 2. 检查公开智能体的分配权限
                SELECT 1 FROM langchain_用户智能体关联表 ua
                JOIN langchain_智能体配置表 lc ON ua.langchain_智能体配置表id = lc.id
                WHERE ua.用户表id = $3
                AND ua.langchain_智能体配置表id = $4
                AND ua.状态 = '启用'
                AND lc.是否公开 = 1
                AND lc.是否启用 = true
            ) as access_check
            """

            结果 = await self.数据库连接池.执行查询(
                验证SQL, (智能体id, 用户id, 用户id, 智能体id)
            )

            if 结果:
                if isinstance(结果[0], dict):
                    权限数量 = 结果[0]["count"]
                else:
                    权限数量 = (
                        结果[0][0] if isinstance(结果[0], (tuple, list)) else 结果[0]
                    )
            else:
                权限数量 = 0

            return 权限数量 > 0

        except Exception as e:
            智能体数据日志器.error(f"验证用户智能体权限失败: {str(e)}")
            raise

    async def 获取用户可用智能体列表(
        self, 用户id: int, 查询参数: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户可用的智能体列表 - 包括分配的私有智能体和所有公开智能体"""
        try:
            # 构建搜索条件
            搜索条件 = ""
            搜索参数 = []
            参数索引 = 2  # 从2开始，因为用户id是$1
            if 查询参数 and 查询参数.get("搜索关键词"):
                搜索关键词 = f"%{查询参数['搜索关键词']}%"
                搜索条件 = f"AND (lc.智能体名称 LIKE ${参数索引} OR lc.智能体描述 LIKE ${参数索引 + 1})"
                搜索参数 = [搜索关键词, 搜索关键词]
                参数索引 += 2

            # 智能体类型字段已移除
            类型条件 = ""
            类型参数 = []

            # 获取总数 - 使用UNION查询分配的私有智能体和公开智能体
            计数SQL = f"""
            SELECT COUNT(*) as total FROM (
                -- 分配给用户的私有智能体
                SELECT DISTINCT lc.id
                FROM langchain_用户智能体关联表 ua
                JOIN langchain_智能体配置表 lc ON ua.langchain_智能体配置表id = lc.id
                WHERE ua.用户表id = $1
                AND ua.状态 = '启用'
                AND lc.是否启用 = true
                {搜索条件} {类型条件}

                UNION

                -- 所有公开的智能体
                SELECT DISTINCT lc.id
                FROM langchain_智能体配置表 lc
                WHERE lc.是否公开 = 1
                AND lc.是否启用 = true
                {搜索条件} {类型条件}
            ) as combined_agents
            """

            计数参数 = [用户id] + 搜索参数 + 类型参数 + 搜索参数 + 类型参数
            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(计数参数))

            if 计数结果:
                if isinstance(计数结果[0], dict):
                    总数量 = 计数结果[0]["total"]
                else:
                    总数量 = (
                        计数结果[0][0]
                        if isinstance(计数结果[0], (tuple, list))
                        else 计数结果[0]
                    )
            else:
                总数量 = 0

            # 获取分页参数
            页码 = 查询参数.get("页码", 1) if 查询参数 else 1
            每页数量 = 查询参数.get("每页数量", 10) if 查询参数 else 10
            偏移量 = (页码 - 1) * 每页数量

            # 获取详细数据 - 使用UNION查询并标记访问类型
            查询SQL = f"""
            SELECT
                id, 智能体名称, 智能体描述, 是否公开,
                访问类型, 分配类型, 备注, 分配时间, 使用次数, 最后使用时间
            FROM (
                -- 分配给用户的智能体（包括私有和公开）
                SELECT DISTINCT
                    lc.id, lc.智能体名称, lc.智能体描述, lc.是否公开,
                    '分配访问' as 访问类型,
                    ua.分配类型, ua.备注, ua.创建时间 as 分配时间,
                    (SELECT COUNT(*) FROM langchain_对话记录表 cr
                     WHERE cr.用户表id = $1 AND cr.langchain_智能体配置表id = lc.id) as 使用次数,
                    (SELECT MAX(cr.创建时间) FROM langchain_对话记录表 cr
                     WHERE cr.用户表id = $1 AND cr.langchain_智能体配置表id = lc.id) as 最后使用时间,
                    ua.创建时间 as 排序时间
                FROM langchain_用户智能体关联表 ua
                JOIN langchain_智能体配置表 lc ON ua.langchain_智能体配置表id = lc.id
                WHERE ua.用户表id = $1
                AND ua.状态 = '启用'
                AND lc.是否启用 = true
                {搜索条件}

                UNION

                -- 公开智能体（排除已分配的）
                SELECT DISTINCT
                    lc.id, lc.智能体名称, lc.智能体描述, lc.是否公开,
                    '公开访问' as 访问类型,
                    NULL as 分配类型, NULL as 备注, NULL as 分配时间,
                    (SELECT COUNT(*) FROM langchain_对话记录表 cr
                     WHERE cr.用户表id = $1 AND cr.langchain_智能体配置表id = lc.id) as 使用次数,
                    (SELECT MAX(cr.创建时间) FROM langchain_对话记录表 cr
                     WHERE cr.用户表id = $1 AND cr.langchain_智能体配置表id = lc.id) as 最后使用时间,
                    lc.创建时间 as 排序时间
                FROM langchain_智能体配置表 lc
                WHERE lc.是否公开 = 1
                AND lc.是否启用 = true
                AND lc.id NOT IN (
                    SELECT ua.langchain_智能体配置表id
                    FROM langchain_用户智能体关联表 ua
                    WHERE ua.用户表id = $1 AND ua.状态 = '启用'
                )
                {搜索条件}
            ) as combined_result
            ORDER BY 排序时间 DESC
            LIMIT ${参数索引} OFFSET ${参数索引 + 1}
            """

            查询参数列表 = (
                [用户id]
                + 搜索参数
                + 类型参数
                + 搜索参数
                + 类型参数
                + [每页数量, 偏移量]
            )
            结果 = await self.数据库连接池.执行查询(查询SQL, tuple(查询参数列表))

            智能体数据日志器.info(
                f"获取用户 {用户id} 可用智能体列表成功，总数: {总数量}，返回: {len(结果) if 结果 else 0}"
            )
            return list(结果) if 结果 else [], 总数量

        except Exception as e:
            智能体数据日志器.error(f"获取用户可用智能体列表失败: {str(e)}")
            raise

    async def 获取用户智能体使用统计(
        self, 用户id: int, 时间范围: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """获取用户智能体使用统计"""
        try:
            # 构建时间条件
            时间条件 = ""
            时间参数 = [用户id]
            参数索引 = 2

            if 时间范围:
                if 时间范围.get("开始时间"):
                    时间条件 += " AND cr.创建时间 >= $1"
                    时间参数.append(时间范围["开始时间"])
                    参数索引 += 1
                if 时间范围.get("结束时间"):
                    时间条件 += " AND cr.创建时间 <= $1"
                    时间参数.append(时间范围["结束时间"])
                    参数索引 += 1

            # 获取总体统计
            总体统计SQL = f"""
            SELECT
                COUNT(*) as 总对话次数,
                COUNT(DISTINCT langchain_智能体配置表id) as 使用智能体数量,
                AVG(处理时长) as 平均处理时长,
                MAX(创建时间) as 最后使用时间
            FROM langchain_对话记录表 cr
            WHERE cr.用户表id = $1 {时间条件}
            """

            总体结果 = await self.数据库连接池.执行查询(总体统计SQL, tuple(时间参数))
            总体统计 = 总体结果[0] if 总体结果 else {}

            # 获取按智能体分组的统计
            智能体统计SQL = f"""
            SELECT
                lc.id, lc.智能体名称,
                COUNT(*) as 对话次数,
                AVG(cr.处理时长) as 平均处理时长,
                MAX(cr.创建时间) as 最后使用时间,
                MIN(cr.创建时间) as 首次使用时间
            FROM langchain_对话记录表 cr
            JOIN langchain_智能体配置表 lc ON cr.langchain_智能体配置表id = lc.id
            WHERE cr.用户表id = $1 {时间条件}
            GROUP BY lc.id, lc.智能体名称
            ORDER BY 对话次数 DESC
            """

            智能体结果 = await self.数据库连接池.执行查询(
                智能体统计SQL, tuple(时间参数)
            )
            智能体统计 = list(智能体结果) if 智能体结果 else []

            return {
                "总体统计": 总体统计,
                "智能体统计": 智能体统计,
                "统计时间": datetime.now().isoformat(),
            }

        except Exception as e:
            智能体数据日志器.error(f"获取用户智能体使用统计失败: {str(e)}")
            raise

    async def 分配智能体给用户详细(
        self,
        智能体id: int,
        用户id列表: List[int],
        权限级别: str = "读写权限",
        分配类型: str = "个人分配",
        备注: str = "",
    ) -> Dict[str, Any]:
        """
        分配智能体给用户（详细版本）

        个人分配：将智能体所有权转移给用户，智能体变为私有
        共享分配：在关联表中添加记录，智能体保持公开状态
        """
        try:
            # 验证用户id是否存在
            if not 用户id列表:
                return {"success": False, "error": "用户id列表不能为空"}

            # 个人分配只能分配给一个用户
            if 分配类型 == "个人分配" and len(用户id列表) > 1:
                return {"success": False, "error": "个人分配只能分配给一个用户"}

            # 检查用户是否存在
            占位符 = ",".join([f"${i + 1}" for i in range(len(用户id列表))])
            查询SQL = f"""
            SELECT id, 昵称, 手机号
            FROM 用户表
            WHERE id IN ({占位符})
            """

            用户结果 = await self.数据库连接池.执行查询(查询SQL, tuple(用户id列表))

            存在的用户信息 = list(用户结果) if 用户结果 else []
            存在的用户id集合 = {用户["id"] for 用户 in 存在的用户信息}
            不存在的用户id = [uid for uid in 用户id列表 if uid not in 存在的用户id集合]

            if 不存在的用户id:
                return {
                    "success": False,
                    "error": f"以下用户id不存在: {不存在的用户id}",
                    "不存在的用户id": 不存在的用户id,
                }

            # 检查智能体是否存在，获取完整信息
            智能体查询SQL = """
            SELECT 智能体名称, 是否启用, 是否公开, 用户表id
            FROM langchain_智能体配置表
            WHERE id = $1
            """
            智能体结果 = await self.数据库连接池.执行查询(智能体查询SQL, (智能体id,))

            if not 智能体结果:
                return {"success": False, "error": f"智能体id {智能体id} 不存在"}

            智能体信息 = 智能体结果[0]

            # 检查智能体状态：只有启用的智能体可以分配
            if not 智能体信息.get("是否启用", False):
                return {
                    "success": False,
                    "error": f"智能体 '{智能体信息['智能体名称']}' 未启用，无法分配",
                }

            # 根据分配类型执行不同的逻辑
            if 分配类型 == "个人分配":
                return await self._执行个人分配(
                    智能体id, 用户id列表[0], 智能体信息, 存在的用户信息[0], 备注
                )
            elif 分配类型 == "共享分配":
                return await self._执行共享分配(
                    智能体id, 用户id列表, 权限级别, 智能体信息, 存在的用户信息, 备注
                )
            else:
                return {"success": False, "error": f"不支持的分配类型: {分配类型}"}

        except Exception as e:
            智能体数据日志器.error(f"分配智能体给用户详细失败: {str(e)}")
            raise

    async def _执行个人分配(
        self, 智能体id: int, 用户id: int, 智能体信息: dict, 用户信息: dict, 备注: str
    ) -> Dict[str, Any]:
        """执行个人分配：将智能体所有权转移给用户"""
        try:
            当前拥有者id = 智能体信息.get("用户表id")

            # 检查是否已经是该用户的智能体
            if 当前拥有者id == 用户id:
                return {
                    "success": False,
                    "error": f"智能体 '{智能体信息['智能体名称']}' 已经属于用户 {用户信息['昵称']}",
                }

            # 更新智能体配置表：转移所有权并设为私有
            更新智能体SQL = """
            UPDATE langchain_智能体配置表
            SET 用户表id = $1, 是否公开 = 0, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """

            await self.数据库连接池.执行更新(更新智能体SQL, (用户id, 智能体id))

            # 清理该智能体的所有共享分配记录（因为现在是私有的）
            清理关联SQL = """
            DELETE FROM langchain_用户智能体关联表
            WHERE langchain_智能体配置表id = $1
            """
            await self.数据库连接池.执行更新(清理关联SQL, (智能体id,))

            智能体数据日志器.info(
                f"个人分配完成: 智能体{智能体id} '{智能体信息['智能体名称']}' 转移给用户{用户id} '{用户信息['昵称']}'"
            )

            return {
                "success": True,
                "分配类型": "个人分配",
                "智能体信息": 智能体信息,
                "成功分配": [用户id],
                "失败分配": [],
                "用户信息": [用户信息],
                "操作说明": f"智能体已转移给 {用户信息['昵称']}，并设为私有状态",
            }

        except Exception as e:
            智能体数据日志器.error(f"执行个人分配失败: {str(e)}")
            return {"success": False, "error": f"个人分配失败: {str(e)}"}

    async def _执行共享分配(
        self,
        智能体id: int,
        用户id列表: List[int],
        权限级别: str,
        智能体信息: dict,
        用户信息列表: List[dict],
        备注: str,
    ) -> Dict[str, Any]:
        """执行共享分配：在关联表中添加记录，要求智能体必须是公开的"""
        try:
            # 检查智能体是否为公开状态
            if not 智能体信息.get("是否公开", False):
                return {
                    "success": False,
                    "error": f"智能体 '{智能体信息['智能体名称']}' 不是公开状态，无法进行共享分配。请先将智能体设为公开状态。",
                }

            # 检查已存在的分配关系
            占位符 = ",".join([f"${i + 2}" for i in range(len(用户id列表))])
            已分配查询SQL = f"""
            SELECT 用户表id, 状态
            FROM langchain_用户智能体关联表
            WHERE langchain_智能体配置表id = $1 AND 用户表id IN ({占位符})
            """
            已分配参数 = [智能体id] + 用户id列表
            已分配结果 = await self.数据库连接池.执行查询(
                已分配查询SQL, tuple(已分配参数)
            )

            已启用用户id = set()
            已禁用用户id = set()

            for 行 in 已分配结果 or []:
                if 行["状态"] == "启用":
                    已启用用户id.add(行["用户表id"])
                elif 行["状态"] == "禁用":
                    已禁用用户id.add(行["用户表id"])

            需要新建分配的用户id = [
                uid
                for uid in 用户id列表
                if uid not in 已启用用户id and uid not in 已禁用用户id
            ]
            需要重新启用的用户id = [uid for uid in 用户id列表 if uid in 已禁用用户id]
            已存在的用户id = [uid for uid in 用户id列表 if uid in 已启用用户id]

            成功分配 = []
            失败分配 = []

            # 处理新建分配
            for 用户id in 需要新建分配的用户id:
                try:
                    插入SQL = """
                    INSERT INTO langchain_用户智能体关联表
                    (用户表id, langchain_智能体配置表id, 权限级别, 分配类型, 备注, 状态, 分配时间, 创建时间, 更新时间)
                    VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """
                    await self.数据库连接池.执行插入(
                        插入SQL, (用户id, 智能体id, 权限级别, "共享分配", 备注, "启用")
                    )
                    成功分配.append(用户id)
                except Exception as e:
                    失败分配.append({"用户id": 用户id, "错误": str(e)})

            # 处理重新启用分配
            for 用户id in 需要重新启用的用户id:
                try:
                    更新SQL = """
                    UPDATE langchain_用户智能体关联表
                    SET 状态 = '启用', 权限级别 = $1, 备注 = $2, 更新时间 = CURRENT_TIMESTAMP
                    WHERE 用户表id = $3 AND langchain_智能体配置表id = $4
                    """
                    await self.数据库连接池.执行更新(
                        更新SQL, (权限级别, 备注, 用户id, 智能体id)
                    )
                    成功分配.append(用户id)
                except Exception as e:
                    失败分配.append({"用户id": 用户id, "错误": str(e)})

            智能体数据日志器.info(
                f"共享分配完成: 智能体{智能体id} -> 成功{len(成功分配)}个用户，跳过{len(已存在的用户id)}个已分配用户"
            )

            return {
                "success": True,
                "分配类型": "共享分配",
                "智能体信息": 智能体信息,
                "成功分配": 成功分配,
                "失败分配": 失败分配,
                "已分配用户id": list(已启用用户id),
                "用户信息": 用户信息列表,
                "操作说明": f"已将公开智能体共享给 {len(成功分配)} 个用户",
            }

        except Exception as e:
            智能体数据日志器.error(f"执行共享分配失败: {str(e)}")
            return {"success": False, "error": f"共享分配失败: {str(e)}"}

    async def 取消智能体分配批量(
        self, 智能体id: int, 用户id列表: List[int]
    ) -> Dict[str, Any]:
        """批量取消智能体分配"""
        try:
            if not 用户id列表:
                return {"success": False, "error": "用户id列表不能为空"}

            成功取消 = []
            失败取消 = []

            for 用户id in 用户id列表:
                try:
                    # 检查分配关系是否存在
                    检查SQL = """
                    SELECT id FROM langchain_用户智能体关联表
                    WHERE 用户表id = $1 AND langchain_智能体配置表id = $2 AND 状态 = '启用'
                    """
                    检查结果 = await self.数据库连接池.执行查询(
                        检查SQL, (用户id, 智能体id)
                    )

                    if 检查结果:
                        # 删除分配关系
                        删除SQL = """
                        DELETE FROM langchain_用户智能体关联表
                        WHERE 用户表id = $1 AND langchain_智能体配置表id = $2
                        """
                        await self.数据库连接池.执行更新(删除SQL, (用户id, 智能体id))
                        成功取消.append(用户id)
                    else:
                        失败取消.append({"用户id": 用户id, "错误": "分配关系不存在"})

                except Exception as e:
                    失败取消.append({"用户id": 用户id, "错误": str(e)})

            智能体数据日志器.info(
                f"批量取消智能体分配完成: 智能体{智能体id} -> 成功{len(成功取消)}个用户"
            )

            return {"success": True, "成功取消": 成功取消, "失败取消": 失败取消}

        except Exception as e:
            智能体数据日志器.error(f"取消智能体分配批量失败: {str(e)}")
            raise

    async def 获取智能体分配用户详细(
        self, 智能体id: int, 分页参数: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取智能体分配的用户详细列表"""
        try:
            # 获取分页参数
            页码 = 分页参数.get("页码", 1) if 分页参数 else 1
            每页数量 = 分页参数.get("每页数量", 10) if 分页参数 else 10
            偏移量 = (页码 - 1) * 每页数量

            # 查询总数
            计数SQL = """
            SELECT COUNT(*) as total
            FROM langchain_用户智能体关联表 ua
            WHERE ua.langchain_智能体配置表id = $1 AND ua.状态 = '启用'
            """
            计数结果 = await self.数据库连接池.执行查询(计数SQL, (智能体id,))
            总数量 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询用户详细信息
            查询SQL = """
            SELECT
                ua.id as 关联id, ua.用户表id, ua.分配类型, ua.备注, ua.状态, ua.创建时间 as 分配时间,
                u.昵称, u.手机号, u.邮箱, u.状态 as 用户状态,
                (SELECT COUNT(*) FROM langchain_对话记录表 cr
                 WHERE cr.用户表id = ua.用户表id AND cr.langchain_智能体配置表id = $1) as 对话次数,
                (SELECT MAX(cr.创建时间) FROM langchain_对话记录表 cr
                 WHERE cr.用户表id = ua.用户表id AND cr.langchain_智能体配置表id = $1) as 最后使用时间
            FROM langchain_用户智能体关联表 ua
            LEFT JOIN 用户表 u ON ua.用户表id = u.id
            WHERE ua.langchain_智能体配置表id = $1 AND ua.状态 = '启用'
            ORDER BY ua.创建时间 DESC
            LIMIT $2 OFFSET $3
            """

            用户列表 = await self.数据库连接池.执行查询(
                查询SQL, (智能体id, 每页数量, 偏移量)
            )

            智能体数据日志器.info(
                f"获取智能体 {智能体id} 分配用户详细成功，总数: {总数量}，返回: {len(用户列表)}"
            )
            return list(用户列表) if 用户列表 else [], 总数量

        except Exception as e:
            智能体数据日志器.error(f"获取智能体分配用户详细失败: {str(e)}")
            raise

    async def 批量验证用户存在(
        self, 用户id列表: List[int]
    ) -> Tuple[List[Dict], List[int]]:
        """批量验证用户是否存在，返回存在的用户信息和不存在的用户id"""
        try:
            if not 用户id列表:
                return [], []

            # 构建IN查询
            占位符 = ",".join([f"${i + 1}" for i in range(len(用户id列表))])
            查询SQL = f"""
            SELECT id, 昵称, 邮箱, 状态
            FROM 用户表
            WHERE id IN ({占位符})
            """

            用户结果 = await self.数据库连接池.执行查询(查询SQL, tuple(用户id列表))

            存在的用户信息 = list(用户结果) if 用户结果 else []
            存在的用户id集合 = {用户["id"] for 用户 in 存在的用户信息}
            不存在的用户id = [
                用户id for 用户id in 用户id列表 if 用户id not in 存在的用户id集合
            ]

            return 存在的用户信息, 不存在的用户id

        except Exception as e:
            智能体数据日志器.error(f"批量验证用户存在失败: {str(e)}")
            return [], 用户id列表

    # 已移除重复的搜索用户函数，现在使用统一的 Postgre_搜索用户 函数

    async def 检查智能体分配关系(
        self, 用户id: int, 智能体id: int
    ) -> Optional[Dict[str, Any]]:
        """检查用户和智能体的分配关系"""
        try:
            查询SQL = """
            SELECT id, 分配类型, 分配时间, 状态
            FROM langchain_用户智能体关联表
            WHERE 用户表id = $1 AND langchain_智能体配置表id = $2
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (用户id, 智能体id))

            if 结果:
                return 结果[0]
            return None

        except Exception as e:
            智能体数据日志器.error(f"检查智能体分配关系失败: {str(e)}")
            return None

    async def 撤销单个智能体分配(self, 分配ID: int) -> Dict[str, Any]:
        """撤销单个智能体分配"""
        try:
            # 首先检查分配是否存在
            检查SQL = """
            SELECT id, 用户表id, langchain_智能体配置表id, 状态
            FROM langchain_用户智能体关联表
            WHERE id = $1
            """
            分配信息 = await self.数据库连接池.执行查询(检查SQL, (分配ID,))

            if not 分配信息:
                return {"success": False, "error": "分配记录不存在"}

            分配记录 = 分配信息[0]

            # 更新分配状态为禁用
            更新SQL = """
            UPDATE langchain_用户智能体关联表
            SET 状态 = '禁用', 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $1
            """
            await self.数据库连接池.执行更新(更新SQL, (分配ID,))

            智能体数据日志器.info(
                f"成功撤销分配: ID={分配ID}, 用户={分配记录['用户表id']}, 智能体={分配记录['langchain_智能体配置表id']}"
            )

            return {"success": True, "message": "撤销分配成功"}

        except Exception as e:
            智能体数据日志器.error(f"撤销单个智能体分配失败: {str(e)}")
            return {"success": False, "error": f"撤销分配失败: {str(e)}"}

    async def 重新启用智能体分配(self, 分配ID: int) -> Dict[str, Any]:
        """重新启用智能体分配"""
        try:
            # 首先检查分配是否存在
            检查SQL = """
            SELECT id, 用户表id, langchain_智能体配置表id, 状态
            FROM langchain_用户智能体关联表
            WHERE id = $1
            """
            分配信息 = await self.数据库连接池.执行查询(检查SQL, (分配ID,))

            if not 分配信息:
                return {"success": False, "error": "分配记录不存在"}

            分配记录 = 分配信息[0]

            # 更新分配状态为启用
            更新SQL = """
            UPDATE langchain_用户智能体关联表
            SET 状态 = '启用', 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $1
            """
            await self.数据库连接池.执行更新(更新SQL, (分配ID,))

            智能体数据日志器.info(
                f"成功重新启用分配: ID={分配ID}, 用户={分配记录['用户表id']}, 智能体={分配记录['langchain_智能体配置表id']}"
            )

            return {"success": True, "message": "重新启用分配成功"}

        except Exception as e:
            智能体数据日志器.error(f"重新启用智能体分配失败: {str(e)}")
            return {"success": False, "error": f"重新启用分配失败: {str(e)}"}

    async def 创建用户智能体分配(self, 分配数据: Dict[str, Any]) -> bool:
        """创建用户智能体分配记录"""
        try:
            插入SQL = """
            INSERT INTO langchain_用户智能体关联表
            (用户表id, langchain_智能体配置表id, 分配类型, 分配时间, 状态, 备注)
            VALUES ($1, $2, $3, $4, $5, $6)
            """

            参数 = (
                分配数据.get("用户表id"),
                分配数据.get("langchain_智能体配置表id"),
                分配数据.get("分配类型", "individual"),
                分配数据.get("分配时间"),
                分配数据.get("状态", "active"),
                分配数据.get("备注", ""),
            )

            影响行数 = await self.数据库连接池.执行更新(插入SQL, 参数)
            return 影响行数 > 0

        except Exception as e:
            智能体数据日志器.error(f"创建用户智能体分配失败: {str(e)}")
            return False

    async def 删除智能体分配关系(self, 用户id: int, 智能体id: int) -> bool:
        """删除用户和智能体的分配关系"""
        try:
            删除SQL = """
            DELETE FROM langchain_用户智能体关联表
            WHERE 用户表id = $1 AND langchain_智能体配置表id = $2
            """

            影响行数 = await self.数据库连接池.执行更新(删除SQL, (用户id, 智能体id))

            return 影响行数 > 0

        except Exception as e:
            智能体数据日志器.error(f"删除智能体分配关系失败: {str(e)}")
            return False

    async def 获取启用的智能体配置列表(self) -> List[Dict[str, Any]]:
        """获取所有启用的智能体配置（用于服务层加载）"""
        try:
            查询SQL = """
            SELECT lc.id, lc.用户表id, lc.智能体名称, lc.智能体描述, lc.模型名称,
                   lc.温度参数, lc.最大令牌数, lc.记忆窗口大小, lc.启用rag, lc.输出格式,
                   lc.自定义回复格式, lc.自定义变量, lc.标签, lc.是否公开, lc.是否启用,
                   lc.系统提示词, lc.用户提示词
            FROM langchain_智能体配置表 lc
            WHERE lc.是否启用 = true
            """

            结果 = await self.数据库连接池.执行查询(查询SQL)
            return 结果 if 结果 else []

        except Exception as e:
            智能体数据日志器.error(f"获取启用的智能体配置列表失败: {str(e)}")
            return []

    def _验证JSON_Schema配置(self, json_schema) -> bool:
        """
        验证JSON Schema配置格式 - Context7最佳实践
        """
        try:
            if not isinstance(json_schema, dict):
                智能体数据日志器.error("❌ JSON Schema必须是字典格式")
                return False

            # 检查必需字段
            if "properties" not in json_schema:
                智能体数据日志器.error("❌ JSON Schema缺少properties字段")
                return False

            properties = json_schema.get("properties", {})
            if not isinstance(properties, dict):
                智能体数据日志器.error("❌ properties字段必须是字典格式")
                return False

            if len(properties) == 0:
                智能体数据日志器.warning("⚠️ JSON Schema没有定义任何属性")
                return False

            # 验证每个属性的基本格式
            for field_name, field_def in properties.items():
                if not isinstance(field_def, dict):
                    智能体数据日志器.error(f"❌ 字段 {field_name} 定义必须是字典格式")
                    return False

                if "type" not in field_def:
                    智能体数据日志器.error(f"❌ 字段 {field_name} 缺少type定义")
                    return False

                # 验证类型值
                valid_types = [
                    "string",
                    "integer",
                    "number",
                    "boolean",
                    "array",
                    "object",
                ]
                if field_def["type"] not in valid_types:
                    智能体数据日志器.error(
                        f"❌ 字段 {field_name} 的类型 {field_def['type']} 不支持"
                    )
                    return False

            智能体数据日志器.info("✅ JSON Schema配置验证通过")
            return True

        except Exception as e:
            智能体数据日志器.error(f"❌ JSON Schema配置验证异常: {str(e)}")
            return False

    def _验证结构化输出配置完整性(self, 智能体数据: Dict[str, Any]) -> bool:
        """
        验证结构化输出配置的完整性 - 新数据结构
        """
        try:
            输出格式 = 智能体数据.get("输出格式", "text")

            # 如果是结构化输出，检查自定义回复格式
            if 输出格式 in ["json", "structured"]:
                自定义回复格式 = 智能体数据.get("自定义回复格式")

                if not 自定义回复格式:
                    智能体数据日志器.warning("⚠️ 结构化输出模式缺少自定义回复格式配置")
                    return False

                # 验证JSON Schema格式
                if not self._验证JSON_Schema配置(自定义回复格式):
                    智能体数据日志器.error("❌ 自定义回复格式JSON Schema验证失败")
                    return False

                智能体数据日志器.info("✅ 结构化输出配置完整性验证通过")
                return True

            # 非结构化输出，无需验证
            return True

        except Exception as e:
            智能体数据日志器.error(f"❌ 结构化输出配置完整性验证异常: {str(e)}")
            return False

    async def 更新智能体启用状态(self, 智能体id: int, 是否启用: bool) -> Dict[str, Any]:
        """更新智能体启用状态"""
        try:
            # 检查智能体是否存在
            检查SQL = (
                "SELECT 智能体名称, 是否启用 FROM langchain_智能体配置表 WHERE id = $1"
            )
            检查结果 = await self.数据库连接池.执行查询(检查SQL, (智能体id,))

            if not 检查结果:
                return {"success": False, "error": f"智能体id {智能体id} 不存在"}

            原状态 = 检查结果[0]["是否启用"]
            智能体名称 = 检查结果[0]["智能体名称"]

            # 更新启用状态
            更新SQL = "UPDATE langchain_智能体配置表 SET 是否启用 = $1, 更新时间 = NOW() WHERE id = $2"
            await self.数据库连接池.执行更新(更新SQL, (是否启用, 智能体id))

            智能体数据日志器.info(
                f"智能体启用状态更新成功: {智能体名称} ({智能体id}) {原状态} -> {是否启用}"
            )

            return {
                "success": True,
                "智能体id": 智能体id,
                "智能体名称": 智能体名称,
                "原状态": 原状态,
                "新状态": 是否启用,
            }

        except Exception as e:
            智能体数据日志器.error(f"更新智能体启用状态失败: {str(e)}")
            raise

    async def 更新智能体知识库关联(self, 智能体id: int, 知识库列表: List[int]) -> bool:
        """更新智能体知识库关联"""
        try:
            智能体数据日志器.info(
                f"📚 开始更新智能体知识库关联: 智能体id={智能体id}, 知识库列表={知识库列表}"
            )

            # 1. 删除现有关联
            删除SQL = """
            DELETE FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1
            """
            await self.数据库连接池.执行更新(删除SQL, (智能体id,))
            智能体数据日志器.info(f"🗑️ 已删除智能体 {智能体id} 的现有知识库关联")

            # 2. 添加新关联
            if 知识库列表:
                插入SQL = """
                INSERT INTO langchain_智能体知识库关联表
                (langchain_智能体配置表id, langchain_知识库表id, 权重, 检索策略, 最大检索数量, 相似度阈值, 状态, 创建时间)
                VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
                """

                # 为每个知识库创建关联记录
                for 索引, 知识id in enumerate(知识库列表):
                    权重 = len(知识库列表) - 索引  # 权重递减
                    await self.数据库连接池.执行插入(
                        插入SQL,
                        (
                            智能体id,
                            int(知识id),
                            权重,
                            "similarity",  # 默认检索策略
                            5,  # 默认最大检索数量
                            0.7,  # 默认相似度阈值
                            "active",  # 状态
                        ),
                    )

                智能体数据日志器.info(
                    f"✅ 已为智能体 {智能体id} 添加 {len(知识库列表)} 个知识库关联"
                )
            else:
                智能体数据日志器.info(f"📝 智能体 {智能体id} 清空了所有知识库关联")

            return True

        except Exception as e:
            智能体数据日志器.error(f"更新智能体知识库关联失败: {str(e)}")
            raise

    async def 获取智能体知识库关联(self, 智能体id: int) -> List[Dict[str, Any]]:
        """获取智能体的知识库关联信息"""
        try:
            智能体数据日志器.info(f"📚 获取智能体知识库关联: 智能体id={智能体id}")

            查询SQL = """
            SELECT
                akr.id as 关联id,
                akr.langchain_智能体配置表id,
                akr.langchain_知识库表id,
                akr.权重,
                akr.检索策略,
                akr.最大检索数量,
                akr.相似度阈值,
                akr.状态,
                akr.启用查询优化,
                akr.查询优化策略,
                akr.查询优化模型id,
                akr.查询优化提示词,
                akr.创建时间
            FROM langchain_智能体知识库关联表 akr
            WHERE akr.langchain_智能体配置表id = $1
            ORDER BY akr.权重 DESC, akr.创建时间 ASC
            """

            关联列表 = await self.数据库连接池.执行查询(查询SQL, (智能体id,))

            智能体数据日志器.info(
                f"✅ 获取智能体知识库关联成功: 智能体id={智能体id}, 关联数量={len(关联列表)}"
            )
            return 关联列表

        except Exception as e:
            智能体数据日志器.error(f"获取智能体知识库关联失败: {str(e)}")
            raise

    async def 添加智能体知识库关联(self, 智能体id: int, 知识库id: int, 关联配置: Optional[Dict[str, Any]] = None) -> bool:
        """添加单个智能体知识库关联"""
        try:
            智能体数据日志器.info(f"📚 添加智能体知识库关联: 智能体id={智能体id}, 知识库id={知识库id}")

            # 检查是否已存在关联
            检查SQL = """
            SELECT id FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1 AND langchain_知识库表id = $2
            """
            现有关联 = await self.数据库连接池.执行查询(检查SQL, (智能体id, 知识库id))

            if 现有关联:
                智能体数据日志器.warning(f"智能体知识库关联已存在: 智能体id={智能体id}, 知识库id={知识库id}")
                return False

            # 设置默认配置
            配置 = 关联配置 or {}
            权重 = 配置.get("权重", 1.0)
            检索策略 = 配置.get("检索策略", "similarity")
            最大检索数量 = 配置.get("最大检索数量", 5)
            相似度阈值 = 配置.get("相似度阈值", 0.7)
            状态 = 配置.get("状态", "active")

            # 查询优化配置
            查询优化配置 = 配置.get("查询优化配置", {})
            启用查询优化 = 1 if 查询优化配置.get("启用", False) else 0
            查询优化策略 = 查询优化配置.get("策略")
            查询优化模型id = 查询优化配置.get("模型id")
            查询优化提示词 = 查询优化配置.get("提示词")

            插入SQL = """
            INSERT INTO langchain_智能体知识库关联表
            (langchain_智能体配置表id, langchain_知识库表id, 权重, 检索策略, 最大检索数量, 相似度阈值, 状态,
             启用查询优化, 查询优化策略, 查询优化模型id, 查询优化提示词, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)
            """

            await self.数据库连接池.执行插入(插入SQL, (
                智能体id, 知识库id, 权重, 检索策略, 最大检索数量, 相似度阈值, 状态,
                启用查询优化, 查询优化策略, 查询优化模型id, 查询优化提示词
            ))

            智能体数据日志器.info(f"✅ 添加智能体知识库关联成功: 智能体id={智能体id}, 知识库id={知识库id}")
            return True

        except Exception as e:
            智能体数据日志器.error(f"添加智能体知识库关联失败: {str(e)}")
            raise

    async def 删除智能体知识库关联(self, 智能体id: int, 知识库id: int) -> bool:
        """删除单个智能体知识库关联"""
        try:
            智能体数据日志器.info(f"📚 删除智能体知识库关联: 智能体id={智能体id}, 知识库id={知识库id}")

            删除SQL = """
            DELETE FROM langchain_智能体知识库关联表
            WHERE langchain_智能体配置表id = $1 AND langchain_知识库表id = $2
            """

            影响行数 = await self.数据库连接池.执行更新(删除SQL, (智能体id, 知识库id))

            if 影响行数 > 0:
                智能体数据日志器.info(f"✅ 删除智能体知识库关联成功: 智能体id={智能体id}, 知识库id={知识库id}")
                return True
            else:
                智能体数据日志器.warning(f"未找到要删除的关联: 智能体id={智能体id}, 知识库id={知识库id}")
                return False

        except Exception as e:
            智能体数据日志器.error(f"删除智能体知识库关联失败: {str(e)}")
            raise

    async def 更新智能体知识库关联配置(self, 智能体id: int, 知识库id: int, 更新配置: Dict[str, Any]) -> bool:
        """更新单个智能体知识库关联的RAG配置"""
        try:
            智能体数据日志器.info(f"📚 更新智能体知识库关联配置: 智能体id={智能体id}, 知识库id={知识库id}")

            # 构建更新字段
            更新字段 = []
            参数值 = []
            参数索引 = 1

            # 基础RAG配置字段
            基础字段映射 = {
                "权重": "权重",
                "检索策略": "检索策略",
                "最大检索数量": "最大检索数量",
                "相似度阈值": "相似度阈值",
                "状态": "状态"
            }

            for 配置字段, 数据库字段 in 基础字段映射.items():
                if 配置字段 in 更新配置:
                    更新字段.append(f"{数据库字段} = ${参数索引}")
                    参数值.append(更新配置[配置字段])
                    参数索引 += 1

            # 查询优化配置
            if "查询优化配置" in 更新配置:
                查询优化配置 = 更新配置["查询优化配置"]
                if isinstance(查询优化配置, dict):
                    if "启用" in 查询优化配置:
                        更新字段.append(f"启用查询优化 = ${参数索引}")
                        参数值.append(1 if 查询优化配置["启用"] else 0)
                        参数索引 += 1

                    if "策略" in 查询优化配置:
                        更新字段.append(f"查询优化策略 = ${参数索引}")
                        参数值.append(查询优化配置["策略"])
                        参数索引 += 1

                    if "模型id" in 查询优化配置:
                        更新字段.append(f"查询优化模型id = ${参数索引}")
                        参数值.append(查询优化配置["模型id"])
                        参数索引 += 1

                    if "提示词" in 查询优化配置:
                        更新字段.append(f"查询优化提示词 = ${参数索引}")
                        参数值.append(查询优化配置["提示词"])
                        参数索引 += 1

            if not 更新字段:
                智能体数据日志器.warning(f"没有有效的更新字段: 智能体id={智能体id}, 知识库id={知识库id}")
                return False

            # 添加WHERE条件参数
            参数值.extend([智能体id, 知识库id])

            更新SQL = f"""
            UPDATE langchain_智能体知识库关联表
            SET {", ".join(更新字段)}
            WHERE langchain_智能体配置表id = ${参数索引} AND langchain_知识库表id = ${参数索引 + 1}
            """

            影响行数 = await self.数据库连接池.执行更新(更新SQL, tuple(参数值))

            if 影响行数 > 0:
                智能体数据日志器.info(f"✅ 更新智能体知识库关联配置成功: 智能体id={智能体id}, 知识库id={知识库id}")
                return True
            else:
                智能体数据日志器.warning(f"未找到要更新的关联: 智能体id={智能体id}, 知识库id={知识库id}")
                return False

        except Exception as e:
            智能体数据日志器.error(f"更新智能体知识库关联配置失败: {str(e)}")
            raise

    async def 获取智能体工具关联(self, 智能体id: int) -> List[Dict[str, Any]]:
        """获取智能体关联的工具列表"""
        try:
            查询SQL = """
            SELECT
                at.工具名称,
                at.工具配置,
                at.启用状态,
                tc.工具描述,
                tc.工具参数
            FROM langchain_智能体工具关联表 at
            LEFT JOIN langchain_工具配置表 tc ON at.工具名称 = tc.工具名称
            WHERE at.langchain_智能体配置表id = $1 AND at.启用状态 = 1
            ORDER BY at.创建时间
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (智能体id,))

            工具列表 = []
            for 行 in 结果:
                工具信息 = {
                    "工具名称": 行["工具名称"],
                    "工具描述": 行["工具描述"],
                    "工具参数": 行["工具参数"],
                    "工具配置": 行["工具配置"],
                    "启用状态": bool(行["启用状态"]),
                }

                # 解析JSON字段
                if 工具信息["工具参数"] and isinstance(工具信息["工具参数"], str):
                    try:
                        工具信息["工具参数"] = json.loads(工具信息["工具参数"])
                    except json.JSONDecodeError:
                        工具信息["工具参数"] = {}

                if 工具信息["工具配置"] and isinstance(工具信息["工具配置"], str):
                    try:
                        工具信息["工具配置"] = json.loads(工具信息["工具配置"])
                    except json.JSONDecodeError:
                        工具信息["工具配置"] = {}

                工具列表.append(工具信息)

            智能体数据日志器.info(
                f"获取智能体 {智能体id} 关联工具成功: {len(工具列表)} 个工具"
            )
            return 工具列表

        except Exception as e:
            智能体数据日志器.error(f"获取智能体工具关联失败: {str(e)}")
            return []

    async def 更新智能体工具关联(self, 智能体id: int, 工具列表: List[str]) -> bool:
        """更新智能体工具关联"""
        try:
            智能体数据日志器.info(
                f"📚 开始更新智能体工具关联: 智能体id={智能体id}, 工具列表={工具列表}"
            )

            # 使用事务处理
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.transaction():
                    # 1. 删除现有关联
                    删除SQL = """
                    DELETE FROM langchain_智能体工具关联表
                    WHERE langchain_智能体配置表id = $1
                    """
                    await 连接.execute(删除SQL, (智能体id,))
                    智能体数据日志器.info(f"🗑️ 已删除智能体 {智能体id} 的现有工具关联")

                    # 2. 添加新关联
                    if 工具列表:
                        插入SQL = """
                        INSERT INTO langchain_智能体工具关联表
                        (langchain_智能体配置表id, 工具名称, 工具配置, 启用状态, 创建时间)
                        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
                        """

                        # 为每个工具创建关联记录
                        for 工具名称 in 工具列表:
                            await 连接.execute(
                                插入SQL,
                                (
                                    智能体id,
                                    工具名称,
                                    "{}",  # 默认空配置
                                    1,  # 启用状态
                                ),
                            )

                        智能体数据日志器.info(
                            f"✅ 已为智能体 {智能体id} 添加 {len(工具列表)} 个工具关联"
                        )
                    else:
                        智能体数据日志器.info(
                            f"📝 智能体 {智能体id} 清空了所有工具关联"
                        )

            return True

        except Exception as e:
            智能体数据日志器.error(f"更新智能体工具关联失败: {str(e)}")
            raise

    async def 获取用户对话历史(
        self,
        用户id: int,
        智能体id: Optional[int] = None,
        页码: int = 1,
        每页数量: int = 20,
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户对话历史"""
        try:
            # 构建查询条件
            查询条件 = ["lr.用户表id = $1"]
            查询参数 = [用户id]
            参数索引 = 2

            if 智能体id:
                查询条件.append(f"lr.langchain_智能体配置表id = ${参数索引}")
                查询参数.append(智能体id)
                参数索引 += 1

            if 开始时间:
                查询条件.append(f"lr.创建时间 >= ${参数索引}")
                查询参数.append(开始时间)
                参数索引 += 1

            if 结束时间:
                查询条件.append(f"lr.创建时间 <= ${参数索引}")
                查询参数.append(结束时间)
                参数索引 += 1

            条件字符串 = " AND ".join(查询条件)

            # 计算偏移量
            偏移量 = (页码 - 1) * 每页数量

            # 获取对话历史
            历史SQL = f"""
            SELECT
                lr.id,
                lr.会话id,
                lr.用户消息,
                lr.智能体回复,
                lr.创建时间,
                lr.处理时长,
                ac.智能体名称,
                ac.智能体描述
            FROM langchain_对话记录表 lr
            LEFT JOIN langchain_智能体配置表 ac ON lr.langchain_智能体配置表id = ac.id
            WHERE {条件字符串}
            ORDER BY lr.创建时间 DESC
            LIMIT {每页数量} OFFSET {偏移量}
            """

            对话历史 = await self.数据库连接池.执行查询(历史SQL, tuple(查询参数))

            # 获取总数量
            计数SQL = f"""
            SELECT COUNT(*) as 总数量
            FROM langchain_对话记录表 lr
            WHERE {条件字符串}
            """

            计数结果 = await self.数据库连接池.执行查询(计数SQL, tuple(查询参数))
            总数量 = 计数结果[0]["总数量"] if 计数结果 else 0

            return list(对话历史) if 对话历史 else [], 总数量

        except Exception as e:
            智能体数据日志器.error(f"获取用户对话历史失败: {str(e)}")
            raise

    async def 清空智能体记忆(
        self,
        用户id: int,
        智能体id: int,
        会话id: Optional[str] = None
    ) -> bool:
        """清空智能体记忆"""
        try:
            # 构建删除条件
            删除条件 = ["用户表id = $1", "langchain_智能体配置表id = $2"]
            删除参数 = [用户id, 智能体id]

            if 会话id:
                删除条件.append("会话id = $3")
                删除参数.append(会话id)

            条件字符串 = " AND ".join(删除条件)

            # 删除对话记录
            删除SQL = f"""
            DELETE FROM langchain_对话记录表
            WHERE {条件字符串}
            """

            删除结果 = await self.数据库连接池.执行更新(删除SQL, tuple(删除参数))

            智能体数据日志器.info(
                f"✅ 清空智能体记忆成功: 用户{用户id}, 智能体{智能体id}, 会话{会话id}, 删除{删除结果}条记录"
            )
            return True

        except Exception as e:
            智能体数据日志器.error(f"清空智能体记忆失败: {str(e)}")
            raise


# 创建全局智能体数据层实例
LangChain智能体数据层实例 = LangChain智能体数据层()
