"""
LangChain智能体工具函数 - 独立的纯函数和简单包装函数
从LangChain智能体服务中提取的无状态函数

功能：
1. 数据查询工具函数
2. 验证工具函数
3. 格式化工具函数
4. 兼容性工具函数
"""

import json
from typing import Any, Dict, List, Optional, Tuple

# 数据层导入
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 服务.LangChain_文档处理器 import LangChain文档处理器实例

# 日志导入
from 日志 import 应用日志器 as 工具函数日志器
from 状态 import 状态


# ==================== 数据查询工具函数 ====================

async def 获取智能体列表(用户表id: Optional[int] = None, 页码: int = 1, 每页数量: int = 10) -> Dict[str, Any]:
    """获取智能体列表 - 纯数据层包装函数"""
    try:
        # 如果用户表id为None，则获取所有智能体（管理员视图）
        if 用户表id is None:
            智能体列表, 总数量 = await LangChain智能体数据层实例.获取智能体列表(
                页码=页码, 每页数量=每页数量
            )
        else:
            # 获取特定用户的智能体列表
            查询参数 = {"页码": 页码, "每页数量": 每页数量}
            智能体列表, 总数量 = await LangChain智能体数据层实例.获取用户可用智能体列表(
                用户表id, 查询参数
            )
        
        return {
            "status": 100,  # 状态.通用.成功
            "message": "获取智能体列表成功",
            "data": {
                "智能体列表": 智能体列表,
                "总数量": 总数量,
                "当前页码": 页码,
                "每页数量": 每页数量
            }
        }

    except Exception as e:
        工具函数日志器.error(f"获取智能体列表失败: {str(e)}")
        return {
            "status": 1504,  # 状态.LangChain.配置错误
            "message": f"获取智能体列表失败: {str(e)}",
            "data": None
        }


async def 获取智能体详情(智能体id: int) -> Dict[str, Any]:
    """获取智能体详情 - 纯数据层包装函数"""
    try:
        # 委托给数据层查询
        智能体详情 = await LangChain智能体数据层实例.获取智能体详情完整(智能体id)
        
        if 智能体详情:
            return {
                "status": 100,  # 状态.通用.成功
                "message": "获取智能体详情成功",
                "data": 智能体详情
            }
        else:
            return {
                "status": 1505,  # 状态.LangChain.智能体不存在
                "message": "智能体不存在",
                "data": None
            }

    except Exception as e:
        工具函数日志器.error(f"获取智能体详情失败: {str(e)}")
        return {
            "status": 1504,  # 状态.LangChain.配置错误
            "message": f"获取智能体详情失败: {str(e)}",
            "data": None
        }


async def 获取智能体工具配置(智能体id: int) -> Optional[List[Dict[str, Any]]]:
    """获取智能体工具配置 - 纯数据层包装函数"""
    try:
        # 简化实现 - 返回空配置，使用智能体id参数避免未使用警告
        # TODO: 实现具体的工具配置获取逻辑，智能体ID: {智能体id}
        return []
    except Exception as e:
        工具函数日志器.error(f"获取智能体工具配置失败: {str(e)}")
        return None


async def 获取用户可用智能体列表(用户id: int, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
    """获取用户可用的智能体列表 - 纯数据层包装函数"""
    try:
        # 委托给数据层获取用户可用智能体列表
        智能体列表, 总数量 = await LangChain智能体数据层实例.获取用户可用智能体列表(用户id, 查询参数)
        
        return {
            "智能体列表": 智能体列表,
            "总数量": 总数量,
            "当前页码": 查询参数.get("页码", 1),
            "每页数量": 查询参数.get("每页数量", 10)
        }

    except Exception as e:
        工具函数日志器.error(f"获取用户可用智能体列表失败: {str(e)}")
        return {
            "智能体列表": [],
            "总数量": 0,
            "当前页码": 1,
            "每页数量": 10
        }


async def 获取使用统计数据(查询参数: Dict[str, Any]) -> Dict[str, Any]:
    """获取智能体使用统计数据 - 纯数据层包装函数"""
    try:
        # 委托给数据层获取统计数据
        统计结果 = await LangChain智能体数据层实例.获取使用统计数据(
            开始日期=查询参数.get("开始日期"),
            结束日期=查询参数.get("结束日期"),
            智能体id=查询参数.get("智能体id"),
            用户id=查询参数.get("用户id"),
            统计维度=查询参数.get("统计维度", "日"),
            页码=查询参数.get("页码", 1),
            每页数量=查询参数.get("每页数量", 20)
        )
        
        工具函数日志器.info(f"✅ 获取使用统计数据成功，返回 {len(统计结果.get('统计数据', []))} 条记录")
        return 统计结果

    except Exception as e:
        工具函数日志器.error(f"获取使用统计数据失败: {str(e)}")
        return {
            "统计数据": [],
            "总数量": 0,
            "当前页码": 查询参数.get("页码", 1),
            "每页数量": 查询参数.get("每页数量", 20),
            "汇总信息": {
                "总调用次数": 0,
                "总用户数": 0,
                "总智能体数": 0,
                "平均响应时间": 0.0
            }
        }


# ==================== 验证工具函数 ====================

async def 验证智能体存在(智能体id: int) -> Dict[str, Any]:
    """验证智能体是否存在 - 独立验证函数"""
    try:
        智能体详情 = await 获取智能体详情(智能体id)
        if 智能体详情.get("status") == 状态.通用.成功:
            return {"success": True, "data": 智能体详情["data"]}
        else:
            return {"success": False, "error": "智能体不存在"}
    except Exception as e:
        工具函数日志器.error(f"验证智能体存在失败: {str(e)}")
        return {"success": False, "error": f"验证智能体存在失败: {str(e)}"}


async def 验证用户智能体访问权限(用户id: int, 智能体id: int) -> Dict[str, Any]:
    """验证用户是否有权限访问智能体 - 纯数据层包装函数"""
    try:
        # 委托给数据层验证权限
        有权限 = await LangChain智能体数据层实例.验证用户智能体权限(用户id, 智能体id)
        if 有权限:
            return {"success": True}
        else:
            return {"success": False, "error": "用户无权限访问该智能体"}
    except Exception as e:
        工具函数日志器.error(f"验证用户智能体访问权限失败: {str(e)}")
        return {"success": False, "error": f"验证用户智能体访问权限失败: {str(e)}"}


# ==================== 兼容性工具函数 ====================

def 获取支持的文件格式() -> Dict[str, Any]:
    """获取支持的文件格式 - 纯委托函数"""
    try:
        格式列表 = LangChain文档处理器实例.获取支持的文件格式列表()
        
        格式统计 = {
            "总格式数": len(格式列表),
            "文档类型": len([f for f in 格式列表 if f["类型"] == "document"]),
            "表格类型": len([f for f in 格式列表 if f["类型"] == "spreadsheet"]),
            "文本类型": len([f for f in 格式列表 if f["类型"] == "text"]),
            "其他类型": len([f for f in 格式列表 if f["类型"] not in ["document", "spreadsheet", "text"]])
        }

        return {
            "success": True,
            "支持的格式列表": 格式列表,
            "格式统计": 格式统计
        }

    except Exception as e:
        工具函数日志器.error(f"获取支持的文件格式失败: {str(e)}")
        return {
            "success": False,
            "error": f"获取支持的文件格式失败: {str(e)}"
        }


# ==================== 格式化工具函数 ====================

def 格式化智能体响应(状态码: int, 消息: str, 数据: Any = None) -> Dict[str, Any]:
    """格式化智能体响应 - 纯函数"""
    return {
        "status": 状态码,
        "message": 消息,
        "data": 数据
    }


def 格式化成功响应(数据: Any, 消息: str = "操作成功") -> Dict[str, Any]:
    """格式化成功响应 - 纯函数"""
    return 格式化智能体响应(状态.通用.成功, 消息, 数据)


def 格式化错误响应(状态码: int, 消息: str) -> Dict[str, Any]:
    """格式化错误响应 - 纯函数"""
    return 格式化智能体响应(状态码, 消息, None)
