<template>
  <div class="user-contacts">
    <!-- 搜索和操作区域 -->
    <div class="search-filters">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchForm.关键词"
              placeholder="搜索联系人姓名或寄样信息"
              enter-button="搜索"
              size="large"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :span="4">
            <a-button type="default" size="large" @click="handleReset" block>
              <reload-outlined />
              重置
            </a-button>
          </a-col>
          <a-col :span="4">
            <a-button type="primary" size="large" @click="showCreateContactModal" block>
              <plus-outlined />
              新增联系人
            </a-button>
          </a-col>
          <a-col :span="4">
            <a-button type="default" size="large" @click="refreshList" :loading="contactsLoading" block>
              <reload-outlined />
              刷新
            </a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 联系人列表 -->
    <div class="contacts-list">
      <a-card>
        <template #title>
          <div style="display: flex; align-items: center; gap: 12px;">
            <span>联系人列表</span>
            <a-tag color="blue" style="margin: 0;">
              总数：{{ pagination.total || 0 }}
            </a-tag>
          </div>
        </template>

        <a-spin :spinning="contactsLoading" tip="正在加载联系人信息...">
          <a-table
            :dataSource="contactsList"
            :columns="tableColumns"
            :pagination="paginationConfig"
            :rowKey="record => record.用户联系人id"
            @change="handleTableChange"
            size="middle"
            bordered
          >
            <!-- 姓名列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="contact-name">
                  <a-typography-text strong>{{ record.姓名 }}</a-typography-text>
                </div>
              </template>

              <!-- 关联联系方式信息列 -->
              <template v-else-if="column.key === 'contactInfo'">
                <div class="contact-info">
                  <template v-if="record.主要联系方式 || record.主要联系方式类型">
                    <div class="contact-info-content">
                      <div v-if="record.主要联系方式" class="contact-method">
                        <a-typography-text
                          :ellipsis="{ tooltip: record.主要联系方式 }"
                          :content="record.主要联系方式"
                          copyable
                        />
                      </div>
                      <div v-if="record.主要联系方式类型" class="contact-type">
                        <a-tag color="geekblue" size="small">
                          {{ record.主要联系方式类型 }}
                        </a-tag>
                      </div>
                      <div v-if="record.关联平台" class="platform-info">
                        <a-tag :color="getPlatformColor(record.关联平台)" size="small">
                          {{ record.关联平台 }}
                        </a-tag>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <span class="no-contact-info">暂无关联联系方式</span>
                  </template>
                </div>
              </template>

              <!-- 寄样信息列 -->
              <template v-else-if="column.key === 'address'">
                <div class="contact-address">
                  <template v-if="record.寄样信息 && record.寄样信息.length > 0">
                    <div class="shipping-info-preview">
                      <a-typography-text
                        :ellipsis="{ tooltip: getShippingInfoTooltip(record.寄样信息) }"
                        :content="`${record.寄样信息[0].收件人} - ${record.寄样信息[0].地址}`"
                      />
                      <a-tag v-if="record.寄样信息.length > 1" color="blue" size="small" style="margin-left: 8px;">
                        +{{ record.寄样信息.length - 1 }}
                      </a-tag>
                    </div>
                  </template>
                  <template v-else-if="record.寄样地址">
                    <!-- 兼容旧数据 -->
                    <a-typography-text
                      :ellipsis="{ tooltip: record.寄样地址 }"
                      type="warning"
                      :content="record.寄样地址"
                    />
                  </template>
                  <template v-else>
                    <a-typography-text type="secondary">未设置</a-typography-text>
                  </template>
                </div>
              </template>

              <!-- 创建时间列 -->
              <template v-else-if="column.key === 'createTime'">
                <a-typography-text type="secondary">
                  {{ record.创建时间 ? formatDateTime(record.创建时间) : '--' }}
                </a-typography-text>
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="handleViewContact(record)">
                    <eye-outlined />
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="handleEditContact(record)">
                    <edit-outlined />
                    编辑
                  </a-button>
                  <a-popconfirm
                    title="确定要删除这个联系人吗？"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="handleDeleteContact(record)"
                  >
                    <a-button type="link" size="small" danger>
                      <delete-outlined />
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-spin>
      </a-card>
    </div>


    <!-- 联系人详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="联系人详情"
      :width="600"
      :footer="null"
    >
      <div v-if="selectedContact" class="contact-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="联系人ID">
            <a-typography-text copyable>{{ selectedContact.用户联系人id }}</a-typography-text>
          </a-descriptions-item>
          <a-descriptions-item label="姓名">
            <a-typography-text strong>{{ selectedContact.姓名 }}</a-typography-text>
          </a-descriptions-item>
          <a-descriptions-item label="寄样信息">
            <div v-if="selectedContact.寄样信息 && selectedContact.寄样信息.length > 0">
              <div
                v-for="(info, index) in selectedContact.寄样信息"
                :key="index"
                style="margin-bottom: 12px; padding: 12px; background: #fafafa; border-radius: 6px;"
              >
                <div><strong>收件人：</strong>{{ info.收件人 }}</div>
                <div><strong>联系电话：</strong>{{ info.电话 }}</div>
                <div><strong>寄样地址：</strong>{{ info.地址 }}</div>
              </div>
            </div>
            <a-typography-text v-else type="secondary">未设置</a-typography-text>
          </a-descriptions-item>
          <a-descriptions-item label="关联联系方式信息">
            <div v-if="selectedContact.关联联系方式信息" class="contact-detail-container">
              <!-- 联系方式基础信息卡片 -->
              <a-card size="small" class="contact-basic-card" :bordered="false">
                <template #title>
                  <div class="card-title">
                    <phone-outlined class="title-icon" />
                    <span>联系方式基础信息</span>
                  </div>
                </template>

                <div class="contact-basic-info">
                  <a-row :gutter="[16, 12]">
                    <a-col :span="12" v-if="selectedContact.关联联系方式信息.联系方式">
                      <div class="info-row">
                        <span class="info-label">联系方式</span>
                        <a-typography-text copyable class="contact-text">
                          {{ selectedContact.关联联系方式信息.联系方式 }}
                        </a-typography-text>
                      </div>
                    </a-col>
                    <a-col :span="12" v-if="selectedContact.关联联系方式信息.联系方式类型">
                      <div class="info-row">
                        <span class="info-label">联系类型</span>
                        <a-tag color="geekblue" class="contact-type-tag">
                          {{ selectedContact.关联联系方式信息.联系方式类型 }}
                        </a-tag>
                      </div>
                    </a-col>
                    <a-col :span="12" v-if="selectedContact.关联联系方式信息.平台">
                      <div class="info-row">
                        <span class="info-label">所属平台</span>
                        <a-tag :color="getPlatformColor(selectedContact.关联联系方式信息.平台)" class="platform-tag">
                          {{ selectedContact.关联联系方式信息.平台 }}
                        </a-tag>
                      </div>
                    </a-col>
                    <a-col :span="12" v-if="selectedContact.关联联系方式信息.平台账号">
                      <div class="info-row">
                        <span class="info-label">平台账号</span>
                        <a-typography-text copyable class="account-text">
                          {{ selectedContact.关联联系方式信息.平台账号 }}
                        </a-typography-text>
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </a-card>

              <!-- 个人标签信息卡片 -->
              <a-card
                v-if="selectedContact.关联联系方式信息.个人标签 && selectedContact.关联联系方式信息.个人标签.length > 0"
                size="small"
                class="contact-tags-card"
                :bordered="false"
              >
                <template #title>
                  <div class="card-title">
                    <tag-outlined class="title-icon" />
                    <span>个人标签</span>
                  </div>
                </template>

                <div class="contact-tags-content">
                  <div class="tags-wrapper">
                    <a-tag
                      v-for="tag in selectedContact.关联联系方式信息.个人标签"
                      :key="tag"
                      color="processing"
                      class="contact-tag"
                    >
                      {{ tag }}
                    </a-tag>
                  </div>
                </div>
              </a-card>

              <!-- 备注信息卡片 -->
              <a-card
                v-if="selectedContact.关联联系方式信息.个人备注 || selectedContact.关联联系方式信息.补充信息"
                size="small"
                class="contact-notes-card"
                :bordered="false"
              >
                <template #title>
                  <div class="card-title">
                    <file-text-outlined class="title-icon" />
                    <span>备注信息</span>
                  </div>
                </template>

                <div class="contact-notes-content">
                  <div v-if="selectedContact.关联联系方式信息.个人备注" class="note-section">
                    <div class="note-title">个人备注</div>
                    <div class="note-content">
                      <a-typography-paragraph
                        :ellipsis="{ rows: 2, expandable: true, symbol: '展开' }"
                        :content="selectedContact.关联联系方式信息.个人备注"
                        class="note-text"
                      />
                    </div>
                  </div>

                  <div v-if="selectedContact.关联联系方式信息.补充信息" class="note-section">
                    <div class="note-title">补充信息</div>
                    <div class="note-content">
                      <a-typography-paragraph
                        :ellipsis="{ rows: 2, expandable: true, symbol: '展开' }"
                        :content="selectedContact.关联联系方式信息.补充信息"
                        class="note-text"
                      />
                    </div>
                  </div>
                </div>
              </a-card>
            </div>

            <!-- 无关联联系方式信息时的空状态 -->
            <div v-else class="no-contact-state">
              <a-empty
                :image="false"
                description="暂无关联联系方式信息"
                class="empty-state"
              >
                <template #image>
                  <phone-outlined class="empty-icon" />
                </template>
              </a-empty>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            <a-typography-text type="secondary">
              {{ selectedContact.创建时间 ? formatDateTime(selectedContact.创建时间) : '--' }}
            </a-typography-text>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 新增联系人弹窗 -->
    <a-modal
      v-model:open="createContactModalVisible"
      :title="createForm.isEditMode ? '编辑联系人' : '新增联系人'"
      width="1000px"
      :footer="null"
      :maskClosable="false"
      @cancel="handleCreateContactCancel"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="handleCreateContactSubmit"
      >
        <!-- 联系人基本信息 -->
        <a-divider orientation="left">联系人基本信息</a-divider>

        <a-form-item label="联系人姓名" name="姓名" required>
          <a-input
            v-model:value="createForm.姓名"
            placeholder="请输入联系人姓名"
            :maxlength="50"
            show-count
          />
        </a-form-item>

        <!-- 寄样信息区域 -->
        <a-form-item label="寄样信息">
          <div class="shipping-info-section">
            <div
              v-for="(info, index) in createForm.寄样信息"
              :key="index"
              class="shipping-info-item"
            >
              <a-card size="small" :title="`寄样信息 ${index + 1}`">
                <template #extra>
                  <a-button
                    type="text"
                    danger
                    size="small"
                    @click="removeShippingInfo(index)"
                    :disabled="createForm.寄样信息.length <= 1"
                  >
                    删除
                  </a-button>
                </template>

                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item
                      :name="['寄样信息', index, '收件人']"
                      label="收件人"
                      :rules="[{ required: true, message: '请输入收件人' }]"
                    >
                      <a-input
                        v-model:value="info.收件人"
                        placeholder="请输入收件人姓名"
                        :maxlength="50"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      :name="['寄样信息', index, '电话']"
                      label="联系电话"
                      :rules="[{ required: true, message: '请输入联系电话' }]"
                    >
                      <a-input
                        v-model:value="info.电话"
                        placeholder="请输入联系电话"
                        :maxlength="20"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      :name="['寄样信息', index, '地址']"
                      label="寄样地址"
                      :rules="[{ required: true, message: '请输入寄样地址' }]"
                    >
                      <a-textarea
                        v-model:value="info.地址"
                        placeholder="请输入详细地址"
                        :rows="2"
                        :maxlength="200"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-card>
            </div>

            <a-button
              type="dashed"
              block
              @click="addShippingInfo"
              style="margin-top: 16px;"
            >
              <plus-outlined />
              添加寄样信息
            </a-button>
          </div>
        </a-form-item>

        <!-- 编辑模式：联系方式信息编辑 -->
        <template v-if="createForm.isEditMode">
          <a-divider orientation="left">联系方式信息编辑</a-divider>

          <!-- 显示平台信息（只读） -->
          <template v-if="createForm.关联达人平台信息">
            <a-form-item label="关联平台信息">
              <a-card size="small" class="platform-info-card">
                <div class="platform-info-content">
                  <a-row :gutter="16">
                    <a-col :span="8">
                      <div class="info-item">
                        <span class="info-label">平台：</span>
                        <a-tag color="blue">{{ createForm.关联达人平台信息.平台 || '未知' }}</a-tag>
                      </div>
                    </a-col>
                    <a-col :span="8">
                      <div class="info-item">
                        <span class="info-label">平台账号：</span>
                        <span class="info-value">{{ createForm.关联达人平台信息.平台账号 || '未知' }}</span>
                      </div>
                    </a-col>
                    <a-col :span="8">
                      <div class="info-item">
                        <span class="info-label">认领时间：</span>
                        <span class="info-value">
                          {{ createForm.关联达人平台信息.认领时间 ?
                             new Date(createForm.关联达人平台信息.认领时间).toLocaleDateString() : '未知' }}
                        </span>
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </a-card>
            </a-form-item>
          </template>

          <!-- 联系方式信息编辑区域 -->
          <template v-if="createForm.补充信息id">
            <!-- 有关联联系方式信息，设置为只读状态 -->
            <a-form-item label="联系方式信息">
              <a-alert
                message="该联系人已有关联联系方式信息，不可修改"
                type="warning"
                show-icon
                style="margin-bottom: 16px;"
              />
            </a-form-item>

            <div class="contact-form-fields readonly-fields">
              <a-form-item label="联系方式">
                <a-input
                  v-model:value="createForm.达人联系方式信息.联系方式"
                  placeholder="暂无联系方式"
                  readonly
                  class="readonly-input"
                />
              </a-form-item>

              <a-form-item label="联系方式类型">
                <a-input
                  v-model:value="createForm.达人联系方式信息.联系方式类型"
                  placeholder="暂无类型"
                  readonly
                  class="readonly-input"
                />
              </a-form-item>

              <a-form-item label="个人备注">
                <a-textarea
                  v-model:value="createForm.达人联系方式信息.个人备注"
                  placeholder="暂无备注"
                  :rows="3"
                  readonly
                  class="readonly-textarea"
                />
              </a-form-item>

              <a-form-item label="个人标签">
                <div class="readonly-tags">
                  <template v-if="createForm.达人联系方式信息.个人标签 && createForm.达人联系方式信息.个人标签.length > 0">
                    <a-tag
                      v-for="tag in createForm.达人联系方式信息.个人标签"
                      :key="tag"
                      color="default"
                      class="readonly-tag"
                    >
                      {{ tag }}
                    </a-tag>
                  </template>
                  <span v-else class="no-data-text">暂无标签</span>
                </div>
              </a-form-item>

              <a-form-item label="补充信息">
                <a-textarea
                  v-model:value="createForm.达人联系方式信息.补充信息"
                  placeholder="暂无补充信息"
                  :rows="3"
                  readonly
                  class="readonly-textarea"
                />
              </a-form-item>
            </div>
          </template>

          <template v-else>
            <!-- 无关联联系方式信息，允许添加新的联系方式 -->
            <a-form-item label="联系方式信息">
              <a-alert
                message="可以为该联系人添加联系方式信息"
                type="success"
                show-icon
                style="margin-bottom: 16px;"
              />
            </a-form-item>

            <!-- 添加新联系方式的表单 -->
            <div class="contact-form-fields editable-fields">
              <a-form-item label="联系方式" name="['达人联系方式信息', '联系方式']">
                <a-input
                  v-model:value="createForm.达人联系方式信息.联系方式"
                  placeholder="请输入联系方式"
                  :maxlength="100"
                  show-count
                />
              </a-form-item>

              <a-form-item label="联系方式类型" name="['达人联系方式信息', '联系方式类型']">
                <a-select
                  v-model:value="createForm.达人联系方式信息.联系方式类型"
                  placeholder="请选择联系方式类型"
                >
                  <a-select-option value="微信">微信</a-select-option>
                  <a-select-option value="QQ">QQ</a-select-option>
                  <a-select-option value="手机号">手机号</a-select-option>
                  <a-select-option value="邮箱">邮箱</a-select-option>
                  <a-select-option value="其他">其他</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="个人备注" name="['达人联系方式信息', '个人备注']">
                <a-textarea
                  v-model:value="createForm.达人联系方式信息.个人备注"
                  placeholder="请输入个人备注（可选）"
                  :rows="3"
                  :maxlength="500"
                  show-count
                />
              </a-form-item>

              <a-form-item label="个人标签" name="['达人联系方式信息', '个人标签']">
                <a-select
                  v-model:value="createForm.达人联系方式信息.个人标签"
                  mode="tags"
                  placeholder="请选择或输入个人标签（可选）"
                  :max-tag-count="5"
                >
                  <a-select-option value="专业">专业</a-select-option>
                  <a-select-option value="回复快">回复快</a-select-option>
                  <a-select-option value="配合度高">配合度高</a-select-option>
                  <a-select-option value="价格合理">价格合理</a-select-option>
                  <a-select-option value="长期合作">长期合作</a-select-option>
                  <a-select-option value="内容优质">内容优质</a-select-option>
                  <a-select-option value="粉丝活跃">粉丝活跃</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="补充信息" name="['达人联系方式信息', '补充信息']">
                <a-textarea
                  v-model:value="createForm.达人联系方式信息.补充信息"
                  placeholder="请输入补充信息（可选）"
                  :rows="3"
                  :maxlength="1000"
                  show-count
                />
              </a-form-item>
            </div>
          </template>
        </template>

        <!-- 达人关联区域 (仅新增模式显示) -->
        <template v-if="!createForm.isEditMode">
          <a-divider orientation="left">达人关联设置</a-divider>

        <a-form-item label="关联方式" name="关联方式" required>
          <a-radio-group v-model:value="createForm.关联方式" @change="handleAssociationTypeChange">
            <a-radio value="existing">选择已有达人联系方式</a-radio>
            <a-radio value="create">创建新的达人联系方式</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 选择已有达人联系方式 -->
        <template v-if="createForm.关联方式 === 'existing'">
          <a-form-item label="搜索达人信息" name="搜索关键词">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索联系方式、类型、备注或平台账号"
              enter-button="搜索"
              @search="handleSearchTalentInfo"
              @change="handleSearchInputChange"
              allow-clear
            />
          </a-form-item>

          <a-form-item label="选择达人信息" name="补充信息id" required>
            <a-spin :spinning="searchLoading">
              <div class="talent-list" v-if="talentList.length > 0">
                <div
                  v-for="talent in talentList"
                  :key="talent.补充信息id"
                  class="talent-item"
                  :class="{ active: createForm.补充信息id === talent.补充信息id }"
                  @click="selectTalent(talent)"
                >
                  <div class="talent-info">
                    <div class="contact-method">
                      <strong>{{ talent.联系方式 }}</strong>
                      <a-tag color="blue" size="small">{{ talent.联系方式类型 }}</a-tag>
                    </div>
                    <div class="platform-info" v-if="talent.平台账号">
                      <span class="platform">{{ talent.平台 }}：{{ talent.平台账号 }}</span>
                    </div>
                    <div class="remark" v-if="talent.个人备注">
                      <span class="remark-text">备注：{{ talent.个人备注 }}</span>
                    </div>
                  </div>
                  <div class="select-indicator" v-if="createForm.补充信息id === talent.补充信息id">
                    <check-circle-filled style="color: #1890ff;" />
                  </div>
                </div>
              </div>
              <a-empty v-else-if="searchExecuted && !searchLoading" description="暂无匹配的达人信息" />
              <div v-else-if="!searchExecuted" class="search-tip">
                <a-typography-text type="secondary">请输入关键词搜索达人信息</a-typography-text>
              </div>
            </a-spin>
          </a-form-item>
        </template>

        <!-- 创建新的达人联系方式 -->
        <template v-if="createForm.关联方式 === 'create'">
          <a-form-item name="['达人联系方式信息', '联系方式']">
            <template #label>
              <span style="color: red;">*</span> 联系方式
            </template>
            <a-input
              v-model:value="createForm.达人联系方式信息.联系方式"
              placeholder="请输入联系方式（微信号、手机号等）"
              :maxlength="50"
            />
          </a-form-item>

          <a-form-item name="['达人联系方式信息', '联系方式类型']">
            <template #label>
              <span style="color: red;">*</span> 联系方式类型
            </template>
            <a-select
              v-model:value="createForm.达人联系方式信息.联系方式类型"
              placeholder="请选择联系方式类型"
            >
              <a-select-option value="微信">微信</a-select-option>
              <a-select-option value="手机">手机</a-select-option>
              <a-select-option value="QQ">QQ</a-select-option>
              <a-select-option value="邮箱">邮箱</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="个人备注" name="['达人联系方式信息', '个人备注']">
            <a-textarea
              v-model:value="createForm.达人联系方式信息.个人备注"
              placeholder="请输入个人备注（可选）"
              :rows="3"
              :maxlength="500"
              show-count
            />
          </a-form-item>

          <a-form-item label="个人标签" name="['达人联系方式信息', '个人标签']">
            <a-select
              v-model:value="createForm.达人联系方式信息.个人标签"
              mode="tags"
              placeholder="请输入个人标签（可选）"
              :max-tag-count="5"
            >
              <a-select-option value="专业">专业</a-select-option>
              <a-select-option value="回复快">回复快</a-select-option>
              <a-select-option value="配合度高">配合度高</a-select-option>
              <a-select-option value="价格合理">价格合理</a-select-option>
              <a-select-option value="长期合作">长期合作</a-select-option>
              <a-select-option value="内容优质">内容优质</a-select-option>
              <a-select-option value="粉丝活跃">粉丝活跃</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="补充信息" name="['达人联系方式信息', '补充信息']">
            <a-textarea
              v-model:value="createForm.达人联系方式信息.补充信息"
              placeholder="请输入补充信息（可选）"
              :rows="3"
              :maxlength="1000"
              show-count
            />
          </a-form-item>
        </template>
        </template>

        <!-- 提交按钮 -->
        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="createContactLoading" size="large">
              {{ createForm.isEditMode ? '更新联系人' : '创建联系人' }}
            </a-button>
            <a-button @click="handleCreateContactCancel" size="large">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { userContactApi } from '@/api/contact'
import { formatDateTime } from '@/utils/dateUtils'
import {
  CheckCircleFilled,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  PhoneOutlined,
  PlusOutlined,
  ReloadOutlined,
  TagOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const contactsLoading = ref(false)
const contactsList = ref([])
const detailModalVisible = ref(false)
const selectedContact = ref(null)

// 新增联系人弹窗相关
const createContactModalVisible = ref(false)
const createContactLoading = ref(false)
const createFormRef = ref()

// 搜索表单
const searchForm = reactive({
  关键词: ''
})

// 新增联系人表单
const createForm = reactive({
  姓名: '',
  寄样信息: [{
    收件人: '',
    地址: '',
    电话: ''
  }],
  关联方式: 'existing',
  补充信息id: null,
  达人联系方式信息: {
    联系方式: '',
    联系方式类型: '',
    个人备注: '',
    个人标签: [],
    补充信息: ''
  },
  // 编辑模式相关
  isEditMode: false,
  contactId: null,
  // 关联达人平台信息（用于编辑时显示）
  关联达人平台信息: null
})

// 达人搜索相关
const searchKeyword = ref('')
const searchLoading = ref(false)
const searchExecuted = ref(false)
const talentList = ref([])
let searchTimeout = null

// 表单验证规则
const createFormRules = reactive({
  姓名: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  关联方式: [
    {
      required: true,
      message: '请选择关联方式',
      trigger: 'change',
      validator: (_rule, value) => {
        // 编辑模式下不需要验证关联方式
        if (createForm.isEditMode) {
          return Promise.resolve()
        }
        if (!value) {
          return Promise.reject('请选择关联方式')
        }
        return Promise.resolve()
      }
    }
  ],
  补充信息id: [
    {
      required: true,
      message: '请选择要关联的达人信息',
      trigger: 'change',
      validator: (_rule, value) => {
        // 编辑模式下不需要验证补充信息id
        if (createForm.isEditMode) {
          return Promise.resolve()
        }
        if (createForm.关联方式 === 'existing' && !value) {
          return Promise.reject('请选择要关联的达人信息')
        }
        return Promise.resolve()
      }
    }
  ],
  [['达人联系方式信息', '联系方式']]: [
    {
      validator: (_rule, value) => {
        // 编辑模式下不需要验证达人联系方式
        if (createForm.isEditMode) {
          return Promise.resolve()
        }
        if (createForm.关联方式 === 'create' && (!value || !value.trim())) {
          return Promise.reject('请输入联系方式')
        }
        return Promise.resolve()
      }
    }
  ],
  [['达人联系方式信息', '联系方式类型']]: [
    {
      validator: (_rule, value) => {
        // 编辑模式下不需要验证联系方式类型
        if (createForm.isEditMode) {
          return Promise.resolve()
        }
        if (createForm.关联方式 === 'create' && !value) {
          return Promise.reject('请选择联系方式类型')
        }
        return Promise.resolve()
      }
    }
  ]
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 表格列配置
const tableColumns = [
  {
    title: '姓名',
    key: 'name',
    width: 150,
    fixed: 'left'
  },
  {
    title: '关联联系方式信息',
    key: 'contactInfo',
    width: 250,
    ellipsis: true
  },
  {
    title: '寄样信息',
    key: 'address',
    width: 300,
    ellipsis: true
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]



// 计算属性
const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
}))

// 方法定义
const loadContactsList = async () => {
  contactsLoading.value = true
  try {
    const params = {
      关键词: searchForm.关键词 || undefined
    }

    const response = await userContactApi.getContactList(params)

    if (response.状态码 === 100) {
      contactsList.value = response.数据 || []
      pagination.total = contactsList.value.length
    } else {
      message.error(response.消息 || '获取联系人列表失败')
    }
  } catch (error) {
    console.error('获取联系人列表失败:', error)
    message.error('获取联系人列表失败')
  } finally {
    contactsLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadContactsList()
}

// 重置搜索
const handleReset = () => {
  searchForm.关键词 = ''
  pagination.current = 1
  loadContactsList()
}

// 刷新列表
const refreshList = () => {
  loadContactsList()
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadContactsList()
}





// 显示新增联系人弹窗
const showCreateContactModal = () => {
  // 重置为新增模式的初始状态
  safeResetFormData()
  // 清除验证状态
  if (createFormRef.value) {
    createFormRef.value.clearValidate()
  }
  createContactModalVisible.value = true
}

// 安全重置表单数据结构
const safeResetFormData = () => {
  createForm.姓名 = ''
  // 确保寄样信息始终是正确的对象数组结构
  createForm.寄样信息 = [{
    收件人: '',
    地址: '',
    电话: ''
  }]
  createForm.关联方式 = 'existing'
  createForm.补充信息id = null
  createForm.达人联系方式信息 = {
    联系方式: '',
    联系方式类型: '',
    个人备注: '',
    个人标签: [],
    补充信息: ''
  }
  createForm.isEditMode = false
  createForm.contactId = null
  createForm.关联达人平台信息 = null
  searchKeyword.value = ''
  talentList.value = []
  searchExecuted.value = false
}



// 取消新增联系人
const handleCreateContactCancel = () => {
  createContactModalVisible.value = false
  // 只重置数据，不调用表单重置方法
  safeResetFormData()
}

// 添加寄样信息
const addShippingInfo = () => {
  createForm.寄样信息.push({
    收件人: '',
    地址: '',
    电话: ''
  })
}

// 删除寄样信息
const removeShippingInfo = (index) => {
  if (createForm.寄样信息.length > 1) {
    createForm.寄样信息.splice(index, 1)
  }
}

// 关联方式变化处理
const handleAssociationTypeChange = () => {
  createForm.补充信息id = null
  createForm.达人联系方式信息 = {
    联系方式: '',
    联系方式类型: '',
    个人备注: '',
    个人标签: [],
    补充信息: ''
  }
  searchKeyword.value = ''
  talentList.value = []
  searchExecuted.value = false

  // 清除表单验证错误
  if (createFormRef.value) {
    createFormRef.value.clearValidate()
  }
}

// 搜索达人信息
const handleSearchTalentInfo = async () => {
  if (!searchKeyword.value.trim()) {
    message.warning('请输入搜索关键词')
    return
  }

  searchLoading.value = true
  searchExecuted.value = true

  try {
    const response = await userContactApi.searchTalentInfoForAssociation({
      关键词: searchKeyword.value.trim(),
      页码: 1,
      每页数量: 50
    })

    if (response.状态码 === 100) {
      talentList.value = response.数据.列表 || []
      if (talentList.value.length === 0) {
        message.info('未找到匹配的达人信息')
      }
    } else {
      message.error(response.消息 || '搜索失败')
      talentList.value = []
    }
  } catch (error) {
    console.error('搜索达人信息失败:', error)
    message.error('搜索失败')
    talentList.value = []
  } finally {
    searchLoading.value = false
  }
}

// 搜索输入变化（防抖搜索）
const handleSearchInputChange = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  if (!searchKeyword.value.trim()) {
    talentList.value = []
    searchExecuted.value = false
    createForm.补充信息id = null
    return
  }

  searchTimeout = setTimeout(() => {
    handleSearchTalentInfo()
  }, 500)
}

// 选择达人
const selectTalent = (talent) => {
  createForm.补充信息id = talent.补充信息id
}

// 提交新增联系人表单
const handleCreateContactSubmit = async () => {
  try {
    // 验证表单
    await createFormRef.value.validate()

    // 过滤有效的寄样信息（确保数据类型正确）
    const 有效寄样信息 = createForm.寄样信息.filter(info => {
      // 确保info是对象且包含必要属性
      if (!info || typeof info !== 'object') return false
      if (!info.收件人 || !info.地址 || !info.电话) return false

      return info.收件人.trim() && info.地址.trim() && info.电话.trim()
    })

    // 构建提交数据
    const submitData = {
      姓名: createForm.姓名.trim(),
      寄样信息: 有效寄样信息.length > 0 ? 有效寄样信息 : null,
      关联方式: createForm.关联方式
    }

    // 编辑模式下不需要验证达人关联信息
    if (!createForm.isEditMode) {
      if (createForm.关联方式 === 'existing') {
        if (!createForm.补充信息id) {
          message.error('请选择要关联的达人信息')
          return
        }
        submitData.补充信息id = createForm.补充信息id
      } else {
        if (!createForm.达人联系方式信息.联系方式.trim() || !createForm.达人联系方式信息.联系方式类型) {
          message.error('请完善达人联系方式信息')
          return
        }
        submitData.达人联系方式信息 = {
          联系方式: createForm.达人联系方式信息.联系方式.trim(),
          联系方式类型: createForm.达人联系方式信息.联系方式类型,
          个人备注: createForm.达人联系方式信息.个人备注.trim() || null,
          个人标签: createForm.达人联系方式信息.个人标签.length > 0 ? createForm.达人联系方式信息.个人标签 : null,
          补充信息: createForm.达人联系方式信息.补充信息.trim() || null
        }
      }
    }

    createContactLoading.value = true

    let response
    if (createForm.isEditMode) {
      // 编辑模式：更新联系人和联系方式信息
      const editSubmitData = {
        用户联系人id: createForm.contactId,
        姓名: submitData.姓名,
        寄样信息: submitData.寄样信息
      }

      // 根据权限控制逻辑决定是否提交联系方式信息
      if (createForm.补充信息id) {
        // 已有关联联系方式数据，不允许修改，只更新联系人基本信息
        // 不添加联系方式信息到提交数据中
      } else {
        // 没有关联联系方式数据，允许添加新的联系方式信息
        if (createForm.达人联系方式信息.联系方式 && createForm.达人联系方式信息.联系方式类型) {
          editSubmitData.联系方式信息 = {
            联系方式: createForm.达人联系方式信息.联系方式.trim() || null,
            联系方式类型: createForm.达人联系方式信息.联系方式类型 || null,
            个人备注: createForm.达人联系方式信息.个人备注.trim() || null,
            个人标签: createForm.达人联系方式信息.个人标签.length > 0 ? createForm.达人联系方式信息.个人标签 : null,
            补充信息: createForm.达人联系方式信息.补充信息.trim() || null
          }
        }
      }

      response = await userContactApi.updateContactWithContactInfo(editSubmitData)

      if (response.状态码 === 100) {
        // 根据是否有联系方式信息更新显示不同消息
        const successMessage = createForm.补充信息id
          ? '更新联系人信息成功'
          : '更新联系人和联系方式信息成功'
        message.success(successMessage)
        createContactModalVisible.value = false
        safeResetFormData() // 使用安全重置
        loadContactsList() // 刷新列表
      } else {
        message.error(response.消息 || '更新失败')
      }
    } else {
      // 创建模式：创建联系人并关联达人
      response = await userContactApi.createContactWithTalentAssociation(submitData)

      if (response.状态码 === 100) {
        message.success('创建联系人成功')
        createContactModalVisible.value = false
        safeResetFormData() // 使用安全重置
        loadContactsList() // 刷新列表
      } else {
        message.error(response.消息 || '创建失败')
      }
    }
  } catch (error) {
    console.error('创建联系人失败:', error)
    if (error.errorFields) {
      message.error('请检查表单填写')
    } else {
      message.error('创建失败')
    }
  } finally {
    createContactLoading.value = false
  }
}

// 编辑联系人
const handleEditContact = (record) => {
  // 使用弹窗编辑联系人
  loadContactForEdit(record.用户联系人id)
}

// 加载联系人数据进行编辑
const loadContactForEdit = async (contactId) => {
  try {
    // 先安全重置表单数据结构
    safeResetFormData()

    const response = await userContactApi.getContactDetail(contactId)
    if (response.状态码 === 100) {
      const contactData = response.数据

      // 填充表单数据
      createForm.姓名 = contactData.姓名 || ''

      // 处理寄样信息（增强数据验证）
      if (contactData.寄样信息 && Array.isArray(contactData.寄样信息) && contactData.寄样信息.length > 0) {
        createForm.寄样信息 = contactData.寄样信息
          .filter(info => info && typeof info === 'object') // 过滤无效数据
          .map(info => ({
            收件人: (info.收件人 || '').toString(),
            地址: (info.地址 || '').toString(),
            电话: (info.电话 || '').toString()
          }))

        // 如果过滤后没有有效数据，提供默认结构
        if (createForm.寄样信息.length === 0) {
          createForm.寄样信息 = [{
            收件人: '',
            地址: '',
            电话: ''
          }]
        }
      } else {
        createForm.寄样信息 = [{
          收件人: '',
          地址: '',
          电话: ''
        }]
      }

      // 处理关联联系方式信息
      if (contactData.关联联系方式信息) {
        const 联系方式信息 = contactData.关联联系方式信息
        createForm.关联方式 = 'create'
        createForm.补充信息id = 联系方式信息.补充信息id
        createForm.达人联系方式信息 = {
          联系方式: 联系方式信息.联系方式 || '',
          联系方式类型: 联系方式信息.联系方式类型 || '',
          个人备注: 联系方式信息.个人备注 || '',
          个人标签: 联系方式信息.个人标签 || [],
          补充信息: 联系方式信息.补充信息 || ''
        }
        // 存储达人平台信息用于显示（只读状态）
        createForm.关联达人平台信息 = {
          平台: 联系方式信息.平台,
          平台账号: 联系方式信息.平台账号,
          认领时间: 联系方式信息.认领时间
        }
      } else {
        // 没有关联联系方式信息时，重置相关字段
        createForm.关联方式 = 'create'
        createForm.补充信息id = null
        createForm.达人联系方式信息 = {
          联系方式: '',
          联系方式类型: '',
          个人备注: '',
          个人标签: [],
          补充信息: ''
        }
        createForm.关联达人平台信息 = null
      }

      // 设置编辑模式标识
      createForm.isEditMode = true
      createForm.contactId = contactId

      // 显示弹窗
      createContactModalVisible.value = true
    } else {
      message.error('加载联系人数据失败')
    }
  } catch (error) {
    console.error('加载联系人数据失败:', error)
    message.error('加载联系人数据失败')
  }
}

// 查看联系人详情
const handleViewContact = async (record) => {
  try {
    const response = await userContactApi.getContactDetail(record.用户联系人id)
    if (response.状态码 === 100) {
      selectedContact.value = response.数据
      detailModalVisible.value = true
    } else {
      message.error(response.消息 || '获取联系人详情失败')
    }
  } catch (error) {
    console.error('获取联系人详情失败:', error)
    message.error('获取联系人详情失败')
  }
}

// 删除联系人
const handleDeleteContact = async (record) => {
  try {
    const response = await userContactApi.deleteContact({
      用户联系人id: record.用户联系人id
    })

    if (response.状态码 === 100) {
      message.success('删除联系人成功')
      loadContactsList()
    } else {
      message.error(response.消息 || '删除联系人失败')
    }
  } catch (error) {
    console.error('删除联系人失败:', error)
    message.error('删除联系人失败')
  }
}



// 获取寄样信息的工具提示内容
const getShippingInfoTooltip = (寄样信息列表) => {
  if (!寄样信息列表 || 寄样信息列表.length === 0) {
    return '无寄样信息'
  }

  return 寄样信息列表.map((info, index) =>
    `${index + 1}. ${info.收件人} - ${info.电话}\n   ${info.地址}`
  ).join('\n\n')
}

// 获取平台标签颜色
const getPlatformColor = (platform) => {
  const colorMap = {
    '抖音': 'red',
    '微信': 'green',
    '小红书': 'pink',
    '快手': 'orange',
    '微博': 'blue'
  }
  return colorMap[platform] || 'default'
}

// 页面初始化
onMounted(() => {
  loadContactsList()
})
</script>

<style scoped>
.user-contacts {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

.search-filters {
  margin-bottom: 16px;
}

.search-filters .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.contacts-list .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.contact-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-address {
  max-width: 300px;
}

.shipping-info-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.shipping-info-section {
  max-width: 100%;
}

.shipping-info-item {
  margin-bottom: 16px;
}

.shipping-info-item:last-child {
  margin-bottom: 0;
}

.shipping-info-item .ant-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.shipping-info-item .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.shipping-info-item .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.contact-detail {
  padding: 16px 0;
}

.contact-detail .ant-descriptions {
  margin-top: 16px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 8px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f8f9ff;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.ant-btn-default {
  border-color: #d9d9d9;
  color: #595959;
}

.ant-btn-default:hover {
  border-color: #1890ff;
  color: #1890ff;
  transform: translateY(-1px);
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 模态框样式 */
:deep(.ant-modal) {
  border-radius: 12px;
}

:deep(.ant-modal-header) {
  border-radius: 12px 12px 0 0;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px 20px;
  border-radius: 0 0 12px 12px;
}

/* 表单样式 */
:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #262626;
}

:deep(.ant-input),
:deep(.ant-input-search),
:deep(.ant-select-selector),
:deep(.ant-textarea) {
  border-radius: 6px;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
}

:deep(.ant-input:focus),
:deep(.ant-input-search .ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-textarea:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 描述列表样式 */
:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  width: 120px;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-content) {
  background: #fff;
  color: #595959;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters .ant-col {
    margin-bottom: 8px;
  }

  .contact-address {
    max-width: 200px;
  }

  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
  }
}

/* 新增联系人弹窗样式 */
.shipping-info-section {
  max-width: 100%;
}

.shipping-info-item {
  margin-bottom: 16px;
}

.shipping-info-item:last-child {
  margin-bottom: 0;
}

.shipping-info-item .ant-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.shipping-info-item .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.shipping-info-item .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

/* 达人信息卡片样式 */
.talent-info-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.talent-info-content {
  padding: 8px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 60px;
}

.info-value {
  color: #333;
  font-size: 14px;
}

/* 列表页联系方式信息样式 */
.contact-info {
  max-width: 240px;
}

.contact-info-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-method {
  font-size: 13px;
  color: #333;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.contact-type {
  display: flex;
  align-items: center;
}

.platform-info {
  display: flex;
  align-items: center;
}

.no-contact-info {
  color: #999;
  font-size: 13px;
  font-style: italic;
}

/* 详情弹窗联系方式信息样式 */
.contact-detail-container {
  max-width: 100%;
}

.contact-basic-card,
.contact-tags-card,
.contact-notes-card {
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.contact-basic-card:last-child,
.contact-tags-card:last-child,
.contact-notes-card:last-child {
  margin-bottom: 0;
}

.contact-basic-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.contact-tags-card {
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
}

.contact-notes-card {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.title-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #3b82f6;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #6b7280;
  font-size: 13px;
  min-width: 70px;
  flex-shrink: 0;
}

.platform-tag {
  font-weight: 600;
  border-radius: 12px;
  font-size: 12px;
}

.account-text,
.contact-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #374151;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 2px 6px;
  border-radius: 4px;
}

.time-text {
  color: #6b7280;
  font-size: 13px;
}

.contact-type-tag {
  border-radius: 12px;
  font-size: 12px;
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.contact-tag {
  border-radius: 12px;
  font-size: 12px;
  margin: 0;
}

.note-section {
  margin-bottom: 16px;
}

.note-section:last-child {
  margin-bottom: 0;
}

.note-title {
  font-weight: 600;
  color: #374151;
  font-size: 13px;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.note-content {
  padding-left: 12px;
}

.note-text {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.6;
}

.no-contact-state {
  text-align: center;
  padding: 40px 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.empty-state {
  margin: 0;
}

.empty-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

/* 编辑模式平台信息卡片样式 */
.platform-info-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.platform-info-content {
  padding: 8px 0;
}

/* 联系方式表单字段样式 */
.contact-form-fields {
  margin-top: 16px;
}

.contact-form-fields .ant-form-item {
  margin-bottom: 16px;
}

/* 只读字段样式 */
.readonly-fields {
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  margin-top: 8px;
}

.readonly-input,
.readonly-textarea {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #666 !important;
  cursor: not-allowed;
}

.readonly-input:hover,
.readonly-textarea:hover {
  border-color: #d9d9d9 !important;
}

.readonly-input:focus,
.readonly-textarea:focus {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
}

.readonly-tags {
  min-height: 32px;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.readonly-tag {
  background-color: #e8e8e8 !important;
  border-color: #d9d9d9 !important;
  color: #666 !important;
  cursor: default;
}

.no-data-text {
  color: #999;
  font-style: italic;
  font-size: 14px;
}

/* 可编辑字段样式 */
.editable-fields {
  background-color: #fff;
  border: 1px solid #e8f4fd;
  border-radius: 6px;
  padding: 16px;
  margin-top: 8px;
}

.talent-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px;
}

.talent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.talent-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.talent-item.active {
  border-color: #1890ff;
  background-color: #f6ffed;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.talent-item:last-child {
  margin-bottom: 0;
}

.talent-info {
  flex: 1;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.contact-method strong {
  font-size: 16px;
  color: #262626;
}

.platform-info {
  margin-bottom: 4px;
}

.platform {
  font-size: 14px;
  color: #666;
}

.remark {
  margin-bottom: 0;
}

.remark-text {
  font-size: 12px;
  color: #999;
}

.select-indicator {
  font-size: 20px;
}

.search-tip {
  text-align: center;
  padding: 40px 20px;
}

.ant-divider-horizontal.ant-divider-with-text-left::before {
  width: 5%;
}

.ant-divider-horizontal.ant-divider-with-text-left::after {
  width: 95%;
}

@media (max-width: 576px) {
  .user-contacts {
    padding: 0 8px;
  }

  .search-filters .ant-row {
    flex-direction: column;
  }

  .search-filters .ant-col {
    width: 100%;
    margin-bottom: 8px;
  }
}
</style>
