import traceback
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse

import 状态  # 未使用的导入
from 依赖项.认证 import 获取当前用户  # 未使用的导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.响应模型 import 统一响应模型  # 未使用的导入
from 数据模型.样品模型 import (
    更新快递单号请求模型,
    更新快递状态请求模型,
    样品信息查询请求模型,
    样品信息请求模型,
    样品审核请求模型,
    样品详情请求模型,
    物流查询请求模型,
)
from 日志 import 错误日志器  # 未使用的导入
from 服务.异步样品信息服务 import 异步样品信息服务  # 未使用的导入

样品路由 = APIRouter()


@样品路由.post(
    "/get_express_info",
    summary="获取物流信息",
    description="根据快递公司、单号和电话查询物流信息",
)
async def 获取物流信息(请求数据: 物流查询请求模型, 用户: dict = Depends(获取当前用户)):
    """
    获取物流跟踪信息

    功能：
        查询快递物流信息，包含查询次数限制机制

    参数:
        请求数据: 包含快递公司代码、单号和可选的收件人电话
        用户: 通过依赖注入的当前登录用户信息

    返回:
        物流跟踪信息
    """
    try:
        # 获取用户id
        用户id = 用户["id"]

        # 检查用户是否为管理员（管理员不受次数限制）
        是否管理员 = 用户.get("is_admin", 0) == 1

        if not 是否管理员:
            # 检查快递查询次数限制
            查询次数检查结果 = await 检查快递查询次数限制(用户id)

            if not 查询次数检查结果["可以查询"]:
                return JSONResponse(
                    content=统一响应模型.失败(
                        状态.通用.超出限制, 查询次数检查结果["错误信息"]
                    ).转字典()
                )

        # 创建服务实例
        样品服务 = 异步样品信息服务()

        快递公司代码 = await 样品服务.获取快递公司代码(请求数据.快递单号)

        # 调用服务获取物流信息
        结果 = await 样品服务.获取物流信息(
            快递公司代码, 请求数据.快递单号, 请求数据.收件人电话
        )

        # 查询成功后记录查询行为（仅在查询成功时记录）
        if 结果.get("status") == 状态.通用.成功 and not 是否管理员:
            await 记录快递查询行为(用户id, 请求数据.快递单号, 快递公司代码)

        # 返回结果
        return JSONResponse(
            content=统一响应模型.成功(
                结果.get("data"), 结果.get("message", "获取物流信息成功")
            ).转字典()
        )

    except HTTPException as he:
        # 直接传递HTTP异常
        raise he
    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 其他异常
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"获取物流信息失败: {str(e)}"
            ).转字典(),
        )


@样品路由.post(
    "/add_express_info", summary="保存寄样信息", description="保存或更新寄样信息记录"
)
async def 保存寄样信息(请求数据: 样品信息请求模型, 用户: dict = Depends(获取当前用户)):
    """
    保存或更新寄样信息记录

    参数:
        请求数据: 包含寄样信息的请求数据
        用户: 通过依赖注入的当前登录用户信息

    返回:
        操作结果
    """
    try:
        # 创建服务实例
        样品服务 = 异步样品信息服务()

        # 调用服务保存寄样信息
        结果 = await 样品服务.保存寄样信息(
            收件人=请求数据.收件人,
            地址=请求数据.地址,
            电话=请求数据.电话,
            产品id=请求数据.产品id,
            数量=请求数据.数量,
            快递单号=请求数据.快递单号,
            寄样备注=请求数据.寄样备注,
            样品id=请求数据.样品id,
            用户审核状态=getattr(请求数据, "用户审核状态", 0),
            团队审核状态=getattr(请求数据, "团队审核状态", 0),
            微信产品对接进度表id=请求数据.微信产品对接进度表id,
            快递状态=请求数据.快递状态,
            快递状态变更时间=请求数据.快递状态变更时间,
            用户联系人表id=getattr(请求数据, "用户联系人表id", None),
        )

        # 返回结果
        return JSONResponse(
            content=统一响应模型.成功(
                结果.get("data"), 结果.get("message", "保存寄样信息成功")
            ).转字典()
        )

    except HTTPException as he:
        # 直接传递HTTP异常
        raise he
    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 其他异常
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"保存寄样信息失败: {str(e)}"
            ).转字典(),
        )


# 获取样品信息记录列表
@样品路由.post(
    "/list", summary="获取样品信息记录列表", description="根据条件获取样品信息记录列表"
)
async def 获取样品信息记录列表(
    请求数据: 样品信息查询请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取样品信息记录列表

    参数:
        请求数据: 包含查询条件的请求数据
        用户: 通过依赖注入的当前登录用户信息

    返回:
        样品信息记录列表及分页信息
    """
    try:
        # 创建服务实例
        样品服务 = 异步样品信息服务()

        # 调用服务获取样品信息记录列表
        结果 = await 样品服务.获取样品信息记录列表(
            用户id=用户["id"],
            页码=请求数据.页码,
            每页数量=请求数据.每页数量,
            收件人=请求数据.收件人,
            产品id=请求数据.产品id,
            审核状态=请求数据.审核状态,
            快递单号=请求数据.快递单号,
            快递状态=请求数据.快递状态,
        )

        # 返回结果
        return JSONResponse(
            content=统一响应模型.成功(
                结果.get("data"), 结果.get("message", "获取样品信息记录列表成功")
            ).转字典()
        )

    except HTTPException as he:
        # 处理HTTP异常中的详细信息
        if hasattr(he, "detail") and isinstance(he.detail, dict):
            详细信息 = he.detail
            return JSONResponse(
                status_code=he.status_code,
                content=统一响应模型.失败(
                    详细信息.get("status", 状态.通用.服务器错误),
                    详细信息.get("message", str(he)),
                ).转字典(),
            )
        # 直接传递HTTP异常
        raise he
    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except TypeError as te:
        # 类型错误，通常是因为JSON序列化问题
        错误消息 = str(te)
        if "not JSON serializable" in 错误消息:
            错误日志器.error(f"数据类型序列化错误: {错误消息}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=统一响应模型.失败(
                    状态.通用.服务器错误,
                    f"数据类型无法序列化，可能是日期时间字段未正确转换: {错误消息}",
                ).转字典(),
            )
        else:
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=统一响应模型.失败(
                    状态.通用.服务器错误, f"类型错误: {错误消息}"
                ).转字典(),
            )
    except Exception as e:
        # 其他异常
        错误日志器.error(f"获取样品信息记录列表失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"获取样品信息记录列表失败: {str(e)}"
            ).转字典(),
        )


# 获取样品信息记录详情
@样品路由.post(
    "/detail",
    summary="获取样品信息记录详情",
    description="根据样品信息列表id获取样品信息记录详情",
)
async def 获取样品信息记录详情(
    请求数据: 样品详情请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取样品信息记录详情

    参数:
        请求数据: 包含样品id的请求数据
        用户: 通过依赖注入的当前登录用户信息

    返回:
        样品信息记录详情
    """
    try:
        # 创建服务实例
        样品服务 = 异步样品信息服务()

        # 调用服务获取样品信息记录详情
        结果 = await 样品服务.获取样品信息记录详情(样品id=请求数据.样品id)

        # 返回结果
        return JSONResponse(
            content=统一响应模型.成功(
                结果.get("data"), 结果.get("message", "获取样品信息记录详情成功")
            ).转字典()
        )

    except HTTPException as he:
        # 直接传递HTTP异常
        raise he
    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 其他异常
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"获取样品信息记录详情失败: {str(e)}"
            ).转字典(),
        )


# 审核样品
@样品路由.post("/approve", summary="审核样品", description="根据样品信息列表id审核样品")
async def 审核样品(请求数据: 样品审核请求模型, 用户: dict = Depends(获取当前用户)):
    """
    审核样品

    参数:
        请求数据: 包含样品id和审核结果的请求数据
        用户: 通过依赖注入的当前登录用户信息

    返回:
        审核结果
    """
    try:
        # 创建服务实例
        样品服务 = 异步样品信息服务()

        # 验证用户是否有权限
        if not 用户.get("id"):
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=统一响应模型.失败(状态.通用.失败, "没有审核权限").转字典(),
            )

        # 调用服务审核样品（参数验证在服务层进行）
        结果 = await 样品服务.审核样品(
            样品id=请求数据.样品id,
            审核状态=请求数据.审核状态,
            审核类型=请求数据.审核类型,
        )

        # 检查结果状态
        if 结果.get("status") != 状态.通用.成功:
            错误消息 = 结果.get("message", "审核样品失败")
            # print(f"审核样品失败: {错误消息}")  # DEBUG
            # 根据返回的状态码确定HTTP状态
            if 结果.get("status") == 状态.通用.失败:
                http状态码 = status.HTTP_404_NOT_FOUND
            elif 结果.get("status") == 状态.通用.参数错误:
                http状态码 = status.HTTP_400_BAD_REQUEST
            elif 结果.get("status") == 状态.通用.失败:
                http状态码 = status.HTTP_400_BAD_REQUEST
            else:
                http状态码 = status.HTTP_500_INTERNAL_SERVER_ERROR

            return JSONResponse(
                status_code=http状态码,
                content=统一响应模型.失败(结果.get("status"), 错误消息).转字典(),
            )

        # 返回结果
        # print(f"审核样品成功: 样品id={请求数据.样品id}")  # DEBUG
        return JSONResponse(
            content=统一响应模型.成功(
                结果.get("data"), 结果.get("message", "审核样品成功")
            ).转字典()
        )

    except HTTPException as he:
        # 记录错误
        # print(f"审核样品时发生HTTP异常: {he.detail}")  # DEBUG
        # 直接传递HTTP异常
        raise he
    except ValueError as ve:
        # 记录错误
        print(f"审核样品时发生参数校验错误: {str(ve)}")
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=统一响应模型.失败(
                状态.通用.参数错误, f"参数错误: {str(ve)}"
            ).转字典(),
        )
    except Exception as e:
        # 记录详细错误信息
        错误信息 = traceback.format_exc()
        print(f"审核样品时发生异常: {str(e)}\n{错误信息}")
        # 其他异常
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"审核样品失败: {str(e)}"
            ).转字典(),
        )


@样品路由.post(
    "/update_express_delivery",
    summary="手动更新快递状态",
    description="根据样品信息列表id手动更新快递状态",
)
async def 手动设置样品配送状态(
    请求数据: 更新快递状态请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    手动设置样品配送状态

    参数:
        请求数据: 包含样品id和快递状态的请求数据
        用户: 通过依赖注入的当前登录用户信息

    返回:
        操作结果
    """
    try:
        # 验证快递状态范围
        有效快递状态 = [-1, 0, 1, 2]
        if 请求数据.快递状态 not in 有效快递状态:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content=统一响应模型.失败(
                    状态.通用.参数错误,
                    f"无效的快递状态值: {请求数据.快递状态}，有效值为: -1(已退回)、0(待出库)、1(运输中)、2(已签收)",
                ).转字典(),
            )

        # 创建服务实例
        样品服务 = 异步样品信息服务()

        # 调用服务更新快递状态
        结果 = await 样品服务.更新快递状态(
            样品id=请求数据.样品id, 快递状态=请求数据.快递状态
        )

        # 检查结果状态
        if 结果.get("status") != 状态.通用.成功:
            # 根据返回的状态码确定HTTP状态
            if 结果.get("status") == 状态.通用.数据不存在:
                http状态码 = status.HTTP_404_NOT_FOUND
            elif 结果.get("status") == 状态.通用.参数错误:
                http状态码 = status.HTTP_400_BAD_REQUEST
            else:
                http状态码 = status.HTTP_500_INTERNAL_SERVER_ERROR

            return JSONResponse(
                status_code=http状态码,
                content=统一响应模型.失败(
                    结果.get("status"), 结果.get("message", "更新快递状态失败")
                ).转字典(),
            )

        # 返回成功结果
        return JSONResponse(
            content=统一响应模型.成功(
                结果.get("data"), 结果.get("message", "更新快递状态成功")
            ).转字典()
        )

    except HTTPException as he:
        # 直接传递HTTP异常
        raise he
    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=统一响应模型.失败(
                状态.通用.参数错误, f"参数错误: {str(ve)}"
            ).转字典(),
        )
    except Exception as e:
        # 其他异常
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"更新快递状态失败: {str(e)}"
            ).转字典(),
        )


@样品路由.post("/update_express_number", response_model=Dict[str, Any])
async def 更新快递单号(
    请求: 更新快递单号请求模型, 当前用户: Dict[str, Any] = Depends(获取当前用户)
):
    """
    更新样品信息记录中的快递单号

    参数:
        请求: 包含样品id和快递单号的请求模型
        当前用户: 当前登录的用户信息

    返回:
        更新操作的结果
    """
    try:
        # 检查用户权限
        if not 当前用户:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"status": 状态.通用.未授权, "message": "用户未登录"},
            )

        # 调用服务层方法更新快递单号
        结果 = await 异步样品信息服务.更新快递单号(请求.样品id, 请求.快递单号)
        return 结果

    except HTTPException:
        # 直接重新抛出HTTP异常
        raise

    except Exception as e:
        错误信息 = traceback.format_exc()
        错误日志器.error(f"更新快递单号时发生错误: {str(e)}\n{错误信息}")
        return {"status": 状态.通用.操作失败, "message": f"更新快递单号失败: {str(e)}"}


@样品路由.post(
    "/update_logistics_status",
    summary="更新样品物流状态",
    description="根据物流信息更新样品物流状态",
)
async def 更新样品物流状态(
    请求数据: 样品详情请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    更新样品物流状态信息（自动获取最新物流信息）

    参数:
        请求数据: 包含样品id的请求数据
        用户: 通过依赖注入的当前登录用户信息

    返回:
        更新结果
    """
    try:
        # 检查用户权限
        if not 用户:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"status": 状态.通用.未授权, "message": "用户未登录"},
            )

        # 创建服务实例
        样品服务 = 异步样品信息服务()

        # 先获取样品信息，获取快递单号
        样品结果 = await 样品服务.获取样品信息记录详情(样品id=请求数据.样品id)

        if 样品结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content=统一响应模型.失败(
                    样品结果.get("status"), 样品结果.get("message", "获取样品信息失败")
                ).转字典(),
            )

        样品信息 = 样品结果.get("data")
        快递单号 = 样品信息.get("快递单号")

        if not 快递单号:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content=统一响应模型.失败(
                    状态.通用.参数错误, "样品尚未设置快递单号，无法获取物流信息"
                ).转字典(),
            )

        # 获取快递公司代码
        快递公司代码 = await 样品服务.获取快递公司代码(快递单号)
        if not 快递公司代码:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content=统一响应模型.失败(
                    状态.通用.参数错误, "无法识别快递公司，请检查快递单号是否正确"
                ).转字典(),
            )

        # 获取物流信息
        物流信息结果 = await 样品服务.获取物流信息(
            快递公司代码=快递公司代码, 快递单号=快递单号
        )

        if 物流信息结果.get("status") != 状态.通用.成功:
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=统一响应模型.失败(
                    物流信息结果.get("status"),
                    物流信息结果.get("message", "获取物流信息失败"),
                ).转字典(),
            )

        物流信息 = 物流信息结果.get("data")

        # 调用服务更新物流状态
        结果 = await 样品服务.更新样品物流状态(
            样品id=请求数据.样品id, 物流信息=物流信息
        )

        # 返回结果
        return JSONResponse(
            content=统一响应模型.成功(
                结果.get("data"), 结果.get("message", "更新物流状态成功")
            ).转字典()
        )

    except HTTPException as he:
        # 直接传递HTTP异常
        raise he
    except ValueError as ve:
        # 参数校验错误
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=统一响应模型.失败(状态.通用.参数错误, str(ve)).转字典(),
        )
    except Exception as e:
        # 其他异常
        错误日志器.error(f"更新物流状态失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"更新物流状态失败: {str(e)}"
            ).转字典(),
        )


@样品路由.post("/stats", summary="获取样品统计信息", description="获取用户样品统计数据")
async def 获取样品统计信息(用户: dict = Depends(获取当前用户)):
    """
    获取样品统计信息

    参数:
        用户: 通过依赖注入的当前登录用户信息

    返回:
        样品统计数据
    """
    try:
        # 创建服务实例
        样品服务 = 异步样品信息服务()

        # 获取用户id
        用户id = 用户.get("id")
        if not 用户id:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content=统一响应模型.失败(状态.通用.未授权, "用户信息无效").转字典(),
            )

        # 获取样品统计数据
        统计结果 = await 样品服务.获取用户样品统计(用户id)

        if 统计结果:
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.成功(统计结果, "获取样品统计信息成功").转字典(),
            )
        else:
            # 返回默认统计数据
            默认统计 = {"总数": 0, "待审核": 0, "已发货": 0, "已送达": 0}
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=统一响应模型.成功(默认统计, "获取样品统计信息成功").转字典(),
            )

    except Exception as e:
        错误日志器.error(f"获取样品统计信息失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"获取样品统计信息失败: {str(e)}"
            ).转字典(),
        )


# ==================== 快递查询次数限制相关函数 ====================


async def 检查快递查询次数限制(用户id: int) -> dict:
    """
    检查用户快递查询次数是否超过限制

    参数:
        用户id: 用户id

    返回:
        检查结果字典，包含是否可以查询和相关信息
    """
    try:
        from datetime import date

        # 获取用户的每日快递查询次数限额
        用户查询 = """
                   SELECT 每日快递查询次数, is_admin
                   FROM 用户表
                   WHERE id = $1 \
                   """
        用户结果 = await 异步连接池实例.执行查询(用户查询, (用户id,))

        if not 用户结果:
            return {
                "可以查询": False,
                "错误信息": "用户不存在",
                "剩余次数": 0,
                "总限额": 0,
            }

        用户信息 = 用户结果[0]
        每日限额 = 用户信息.get("每日快递查询次数", 30)  # 默认30次
        是否管理员 = 用户信息.get("is_admin", 0) == 1

        # 管理员不受限制
        if 是否管理员:
            return {
                "可以查询": True,
                "错误信息": "",
                "剩余次数": -1,  # -1表示无限制
                "总限额": -1,
            }

        # 获取今日已查询次数
        今日 = date.today()
        今日查询次数查询 = """
                           SELECT COUNT(*) as 今日查询次数
                           FROM 用户_快递查询记录表
                           WHERE 用户表id = $1
                             AND DATE(查询时间) = $2 \
                           """
        查询次数结果 = await 异步连接池实例.执行查询(今日查询次数查询, (用户id, 今日))

        今日已查询次数 = 查询次数结果[0]["今日查询次数"] if 查询次数结果 else 0
        剩余次数 = max(0, 每日限额 - 今日已查询次数)

        # 检查是否超过限制
        if 今日已查询次数 >= 每日限额:
            return {
                "可以查询": False,
                "错误信息": f"今日快递查询次数已达上限（{每日限额}次），请明日再试",
                "剩余次数": 0,
                "总限额": 每日限额,
                "今日已用": 今日已查询次数,
            }

        return {
            "可以查询": True,
            "错误信息": "",
            "剩余次数": 剩余次数,
            "总限额": 每日限额,
            "今日已用": 今日已查询次数,
        }

    except Exception as e:
        错误日志器.error(f"检查快递查询次数限制失败: {str(e)}", exc_info=True)
        return {
            "可以查询": False,
            "错误信息": "系统错误，请稍后重试",
            "剩余次数": 0,
            "总限额": 0,
        }


async def 记录快递查询行为(
    用户id: int, 快递单号: str, 快递公司代码: str = None
) -> bool:
    """
    记录用户快递查询行为

    参数:
        用户id: 用户id
        快递单号: 快递单号
        快递公司代码: 快递公司代码（可选）

    返回:
        是否记录成功
    """
    try:
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 查找对应的样品记录id（如果存在）
        样品查询 = """
                   SELECT s.id
                   FROM 用户寄样信息表 s
                            LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
                   WHERE s.快递单号 = $1
                     AND p.用户id = $2
                   LIMIT 1 \
                   """
        样品结果 = await 异步连接池实例.执行查询(样品查询, (快递单号, 用户id))

        样品id = 样品结果[0]["id"] if 样品结果 else None

        # 插入查询记录
        插入查询 = """
                   INSERT INTO 用户_快递查询记录表
                       (用户表id, 用户寄样信息表id, 查询时间)
                   VALUES ($1, $2, NOW()) \
                   """

        await 异步连接池实例.执行插入(插入查询, (用户id, 样品id))

        # 记录日志
        from 日志 import 应用日志器

        应用日志器.info(
            f"快递查询记录成功: 用户id={用户id}, 快递单号={快递单号}, 样品id={样品id}"
        )

        return True

    except Exception as e:
        错误日志器.error(f"记录快递查询行为失败: {str(e)}", exc_info=True)
        return False


@样品路由.post(
    "/express-query-quota",
    summary="获取快递查询配额信息",
    description="获取用户的快递查询次数限额和使用情况",
)
async def 获取快递查询配额信息(用户: dict = Depends(获取当前用户)):
    """
    获取快递查询配额信息

    功能：
        获取用户的快递查询次数限额、今日已使用次数、剩余次数等信息

    返回:
        快递查询配额信息
    """
    try:
        用户id = 用户["id"]

        # 获取查询次数信息
        配额信息 = await 检查快递查询次数限制(用户id)

        # 构建返回数据
        返回数据 = {
            "用户id": 用户id,
            "每日限额": 配额信息.get("总限额", 30),
            "今日已用": 配额信息.get("今日已用", 0),
            "剩余次数": 配额信息.get("剩余次数", 0),
            "是否可查询": 配额信息.get("可以查询", False),
            "是否管理员": 配额信息.get("总限额", 0) == -1,
            "配额状态": "无限制"
            if 配额信息.get("总限额", 0) == -1
            else (
                "充足"
                if 配额信息.get("剩余次数", 0) > 5
                else ("紧张" if 配额信息.get("剩余次数", 0) > 0 else "已用完")
            ),
        }

        return JSONResponse(
            content=统一响应模型.成功(返回数据, "获取快递查询配额信息成功").转字典()
        )

    except Exception as e:
        错误日志器.error(f"获取快递查询配额信息失败: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=统一响应模型.失败(
                状态.通用.服务器错误, f"获取快递查询配额信息失败: {str(e)}"
            ).转字典(),
        )
